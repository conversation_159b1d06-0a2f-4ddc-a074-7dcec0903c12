'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Sparkles, 
  Store, 
  Wand2, 
  X, 
  ArrowRight, 
  Lightbulb,
  ShoppingBag,
  Palette,
  Type
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ShopGenerationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (prompt: string) => void;
}

export function ShopGenerationDialog({ isOpen, onClose, onGenerate }: ShopGenerationDialogProps) {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const examplePrompts = [
    "Une boutique de vêtements vintage avec un style rétro et des couleurs chaudes",
    "Un magasin d'électronique moderne avec un design minimaliste et high-tech",
    "Une boutique de produits artisanaux avec une ambiance chaleureuse et naturelle",
    "Un shop de cosmétiques élégant avec des tons roses et dorés",
  ];

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    // Simuler la génération (remplacer par l'API réelle)
    setTimeout(() => {
      onGenerate(prompt);
      setIsGenerating(false);
      onClose();
    }, 3000);
  };

  const handleExampleClick = (example: string) => {
    setPrompt(example);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Overlay */}
        <motion.div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        />

        {/* Dialog */}
        <motion.div
          className="relative w-full max-w-2xl bg-white rounded-2xl shadow-2xl overflow-hidden"
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-brand-500 to-brand-700 px-8 py-6 text-white relative">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
            
            <div className="flex items-center gap-4">
              <motion.div
                className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <Sparkles className="w-8 h-8 text-white" />
              </motion.div>
              
              <div>
                <motion.h2 
                  className="text-2xl font-bold mb-1"
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  Créer ma boutique avec l'IA
                </motion.h2>
                <motion.p 
                  className="text-brand-100"
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  Décrivez votre boutique idéale et laissez l'IA la créer pour vous
                </motion.p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              {/* Features */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div className="flex items-center gap-3 p-4 bg-brand-50 rounded-lg">
                  <div className="w-10 h-10 bg-brand-500 rounded-lg flex items-center justify-center">
                    <Palette className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-neutral-900">Design automatique</p>
                    <p className="text-xs text-neutral-600">Couleurs et style</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-brand-50 rounded-lg">
                  <div className="w-10 h-10 bg-brand-500 rounded-lg flex items-center justify-center">
                    <Type className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-neutral-900">Contenu généré</p>
                    <p className="text-xs text-neutral-600">Textes et descriptions</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-brand-50 rounded-lg">
                  <div className="w-10 h-10 bg-brand-500 rounded-lg flex items-center justify-center">
                    <ShoppingBag className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-neutral-900">Structure optimisée</p>
                    <p className="text-xs text-neutral-600">Layout professionnel</p>
                  </div>
                </div>
              </div>

              {/* Prompt Input */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-3">
                  <Lightbulb className="w-5 h-5 text-brand-600" />
                  <label className="text-sm font-semibold text-neutral-900">
                    Décrivez votre boutique idéale
                  </label>
                </div>
                
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Ex: Une boutique de vêtements vintage avec un style rétro, des couleurs chaudes comme le bordeaux et l'or, spécialisée dans les pièces des années 70-80..."
                  className="w-full h-32 px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent resize-none"
                  disabled={isGenerating}
                />
                
                <p className="text-xs text-neutral-500">
                  Plus votre description est détaillée, plus votre boutique sera personnalisée
                </p>
              </div>

              {/* Example Prompts */}
              <div className="mt-6">
                <p className="text-sm font-medium text-neutral-700 mb-3">Exemples d'inspiration :</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {examplePrompts.map((example, index) => (
                    <motion.button
                      key={index}
                      onClick={() => handleExampleClick(example)}
                      className="text-left p-3 bg-neutral-50 hover:bg-brand-50 border border-neutral-200 hover:border-brand-300 rounded-lg transition-all duration-200 text-sm"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      disabled={isGenerating}
                    >
                      <div className="flex items-start gap-2">
                        <Wand2 className="w-4 h-4 text-brand-500 mt-0.5 flex-shrink-0" />
                        <span className="text-neutral-700">{example}</span>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-3 mt-8">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1 border-neutral-200 text-neutral-600 hover:bg-neutral-50"
                  disabled={isGenerating}
                >
                  Créer manuellement
                </Button>
                
                <Button
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating}
                  className="flex-1 bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-medium"
                >
                  {isGenerating ? (
                    <div className="flex items-center gap-2">
                      <motion.div
                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                      Génération en cours...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Sparkles className="w-4 h-4" />
                      Générer ma boutique
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  )}
                </Button>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
