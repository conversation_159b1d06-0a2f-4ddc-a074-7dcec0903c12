'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Menu, X, User, LogOut } from 'lucide-react';
import { LanguageSelector } from './language-selector';
import { useLanguage } from '@/hooks/use-language';
import { SharyouIcon } from '@/components/icons/sharyou-icon';
import { useAuth } from '@/hooks/use-auth';
import { LoadingButton } from '@/components/auth/loading-button';

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { t, isRTL } = useLanguage();
  const { user, loading, signOut } = useAuth();
  const router = useRouter();

  const navItems = [
    { key: 'features', href: '#ai-builder', id: 'ai-builder' },
    { key: 'testimonials', href: '#testimonials', id: 'testimonials' },
    { key: 'pricing', href: '#why-choose', id: 'why-choose' },
    { key: 'support', href: '#footer', id: 'footer' },
  ];

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 80;
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
    setIsOpen(false);
  };

  // Track active section and scroll position
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 10);

      // Find active section
      const sections = navItems.map(item => item.id);
      let currentSection = '';

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = sectionId;
          }
        }
      }

      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Call once to set initial state

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserMenu) {
        const target = event.target as Element;
        if (!target.closest('[data-user-menu]')) {
          setShowUserMenu(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showUserMenu]);

  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, sectionId: string) => {
    e.preventDefault();
    scrollToSection(sectionId);
  };

  const handleLogin = () => {
    router.push('/login');
    setIsOpen(false);
  };

  const handleRegister = () => {
    router.push('/register');
    setIsOpen(false);
  };

  const handleLogout = async () => {
    await signOut();
    setShowUserMenu(false);
    setIsOpen(false);
  };

  const handleDashboard = () => {
    router.push('/dashboard');
    setShowUserMenu(false);
    setIsOpen(false);
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-lg border-b border-neutral-200 shadow-lg shadow-brand-500/5'
        : 'bg-white/80 backdrop-blur-md border-b border-neutral-100'
    }`}>
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div
            className="flex items-center gap-3 cursor-pointer group"
            onClick={() => scrollToSection('hero')}
          >
            <div className="transition-transform duration-200 group-hover:scale-110">
              <SharyouIcon size={32} />
            </div>
            <span className="text-xl font-display font-bold text-neutral-900 tracking-tight">Sharyou</span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            {navItems.map((item) => (
              <a
                key={item.key}
                href={item.href}
                onClick={(e) => handleNavClick(e, item.id)}
                className={`relative font-medium transition-all duration-300 hover:text-brand-600 ${
                  activeSection === item.id
                    ? 'text-brand-600'
                    : 'text-neutral-600'
                } group`}
              >
                {t(`nav.${item.key}`)}
                <span className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-brand-500 to-accent-500 transition-all duration-300 group-hover:w-full ${
                  activeSection === item.id ? 'w-full' : ''
                }`}></span>
              </a>
            ))}
          </div>

          {/* Right Side */}
          <div className="flex items-center gap-4">
            <LanguageSelector />

            {/* Authentication Section */}
            {!loading && (
              <>
                {user ? (
                  /* User Menu */
                  <div className="hidden md:flex items-center gap-3">
                    <div className="relative" data-user-menu>
                      <Button
                        variant="ghost"
                        onClick={() => setShowUserMenu(!showUserMenu)}
                        className="flex items-center gap-2 hover:bg-brand-50 transition-colors"
                      >
                        <User className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          {user.user_metadata?.first_name || user.email?.split('@')[0]}
                        </span>
                      </Button>

                      {/* Dropdown Menu */}
                      {showUserMenu && (
                        <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-2 z-50">
                          <button
                            onClick={handleDashboard}
                            className="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50 transition-colors"
                          >
                            <User className="w-4 h-4 inline mr-2" />
                            Dashboard
                          </button>
                          <hr className="my-1 border-neutral-200" />
                          <button
                            onClick={handleLogout}
                            className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                          >
                            <LogOut className="w-4 h-4 inline mr-2" />
                            Déconnexion
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  /* Auth Buttons */
                  <div className="hidden md:flex items-center gap-3">
                    <Button
                      variant="ghost"
                      onClick={handleLogin}
                      className="text-neutral-700 hover:text-brand-600 hover:bg-brand-50 transition-all duration-200"
                    >
                      Connexion
                    </Button>
                    <Button
                      onClick={handleRegister}
                      className="bg-gradient-to-r from-brand-500 to-brand-700 hover:from-brand-600 hover:to-brand-800 text-white font-medium shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                    >
                      Inscription
                    </Button>
                  </div>
                )}
              </>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden p-2 hover:bg-gray-100 transition-colors duration-200"
              onClick={() => setIsOpen(!isOpen)}
              aria-label="Toggle menu"
            >
              <div className="relative w-5 h-5">
                <Menu className={`h-5 w-5 absolute transition-all duration-200 ${isOpen ? 'opacity-0 rotate-90' : 'opacity-100 rotate-0'}`} />
                <X className={`h-5 w-5 absolute transition-all duration-200 ${isOpen ? 'opacity-100 rotate-0' : 'opacity-0 -rotate-90'}`} />
              </div>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${
          isOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="py-4 border-t border-gray-200">
            <div className="space-y-2">
              {/* Navigation Links */}
              {navItems.map((item) => (
                <a
                  key={item.key}
                  href={item.href}
                  onClick={(e) => handleNavClick(e, item.id)}
                  className={`block font-medium py-3 px-4 rounded-lg transition-all duration-200 ${
                    activeSection === item.id
                      ? 'text-brand-600 bg-brand-50'
                      : 'text-neutral-600 hover:text-neutral-900 hover:bg-neutral-50'
                  }`}
                >
                  {t(`nav.${item.key}`)}
                </a>
              ))}

              {/* Mobile Authentication */}
              {!loading && (
                <div className="mt-4 pt-4 border-t border-neutral-200">
                  {user ? (
                    /* Mobile User Menu */
                    <div className="space-y-2">
                      <div className="px-4 py-2 text-sm text-neutral-600">
                        Connecté en tant que <span className="font-medium text-neutral-900">
                          {user.user_metadata?.first_name || user.email?.split('@')[0]}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        onClick={handleDashboard}
                        className="w-full justify-start text-neutral-700 hover:bg-neutral-50"
                      >
                        <User className="w-4 h-4 mr-2" />
                        Dashboard
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={handleLogout}
                        className="w-full justify-start text-red-600 hover:bg-red-50"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        Déconnexion
                      </Button>
                    </div>
                  ) : (
                    /* Mobile Auth Buttons */
                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        onClick={handleLogin}
                        className="w-full border-brand-200 text-brand-600 hover:bg-brand-50"
                      >
                        Connexion
                      </Button>
                      <Button
                        onClick={handleRegister}
                        className="w-full bg-gradient-to-r from-brand-500 to-brand-700 hover:from-brand-600 hover:to-brand-800 text-white font-medium"
                      >
                        Inscription
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
}