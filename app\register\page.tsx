'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Mail, User, AlertCircle, CheckCircle } from 'lucide-react';
import { AuthLayout } from '@/components/auth/auth-layout';
import { FormField } from '@/components/auth/form-field';
import { PasswordField } from '@/components/auth/password-field';
import { LoadingButton } from '@/components/auth/loading-button';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useAuth } from '@/hooks/use-auth';

const validationRules = {
  firstName: [
    { required: true, message: 'Le prénom est requis' },
    { minLength: 2, message: 'Le prénom doit contenir au moins 2 caractères' }
  ],
  lastName: [
    { required: true, message: 'Le nom est requis' },
    { minLength: 2, message: 'Le nom doit contenir au moins 2 caractères' }
  ],
  email: [
    { required: true, message: 'L\'email est requis' },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Format d\'email invalide' }
  ],
  password: [
    { required: true, message: 'Le mot de passe est requis' },
    { minLength: 8, message: 'Le mot de passe doit contenir au moins 8 caractères' },
    { 
      custom: (value: string) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/.test(value),
      message: 'Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial'
    }
  ],
  confirmPassword: [
    { required: true, message: 'La confirmation du mot de passe est requise' }
  ],
  acceptTerms: [
    { required: true, message: 'Vous devez accepter les conditions d\'utilisation' }
  ],
  acceptPrivacy: [
    { required: true, message: 'Vous devez accepter la politique de confidentialité' }
  ]
};

export default function RegisterPage() {
  const router = useRouter();
  const { signUp, loading } = useAuth();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    acceptTerms: false,
    acceptPrivacy: false
  });
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
    }

    if (!formData.email) {
      newErrors.email = 'L\'email est requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    if (!formData.acceptTerms) {
      newErrors.acceptTerms = 'Vous devez accepter les conditions d\'utilisation';
    }

    if (!formData.acceptPrivacy) {
      newErrors.acceptPrivacy = 'Vous devez accepter la politique de confidentialité';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (!validateForm()) {
      return;
    }

    const { data, error } = await signUp(formData.email, formData.password, {
      first_name: formData.firstName,
      last_name: formData.lastName
    });

    if (error) {
      setMessage({
        type: 'error',
        text: error.message === 'User already registered'
          ? 'Cet email est déjà utilisé'
          : error.message
      });
    } else if (data.user) {
      setMessage({
        type: 'success',
        text: 'Inscription réussie ! Redirection vers votre dashboard...'
      });
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    }
  };

  return (
    <AuthLayout
      title="Inscription"
      subtitle="Créez votre compte Sharyou"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Message */}
        {message && (
          <div className={`p-4 rounded-lg flex items-center gap-3 ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
            )}
            <span className="text-sm">{message.text}</span>
          </div>
        )}

        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-4">
          <FormField
            id="firstName"
            name="firstName"
            label="Prénom"
            placeholder="Jean"
            value={formData.firstName}
            onChange={(e) => updateField('firstName', e.target.value)}
            error={errors.firstName}
            icon={<User className="w-5 h-5" />}
            required
            autoComplete="given-name"
          />

          <FormField
            id="lastName"
            name="lastName"
            label="Nom"
            placeholder="Dupont"
            value={formData.lastName}
            onChange={(e) => updateField('lastName', e.target.value)}
            error={errors.lastName}
            icon={<User className="w-5 h-5" />}
            required
            autoComplete="family-name"
          />
        </div>

        {/* Email Field */}
        <FormField
          id="email"
          name="email"
          type="email"
          label="Adresse email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={(e) => updateField('email', e.target.value)}
          error={errors.email}
          icon={<Mail className="w-5 h-5" />}
          required
          autoComplete="email"
        />

        {/* Password Field */}
        <PasswordField
          id="password"
          name="password"
          label="Mot de passe"
          placeholder="••••••••"
          value={formData.password}
          onChange={(e) => updateField('password', e.target.value)}
          error={errors.password}
          showStrength={true}
          required
          autoComplete="new-password"
        />

        {/* Confirm Password Field */}
        <PasswordField
          id="confirmPassword"
          name="confirmPassword"
          label="Confirmer le mot de passe"
          placeholder="••••••••"
          value={formData.confirmPassword}
          onChange={(e) => updateField('confirmPassword', e.target.value)}
          error={errors.confirmPassword}
          required
          autoComplete="new-password"
        />

        {/* Terms and Privacy */}
        <div className="space-y-3">
          <label className="flex items-start gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.acceptTerms}
              onChange={(e) => updateField('acceptTerms', e.target.checked)}
              className="w-4 h-4 text-brand-600 border-neutral-300 rounded focus:ring-brand-500 focus:ring-2 mt-0.5"
            />
            <span className="text-sm text-neutral-700">
              J'accepte les{' '}
              <Link href="/terms" className="text-brand-600 hover:text-brand-700 font-medium">
                conditions d'utilisation
              </Link>
              {' '}*
            </span>
          </label>
          {errors.acceptTerms && (
            <p className="text-sm text-red-600 ml-7">{errors.acceptTerms}</p>
          )}

          <label className="flex items-start gap-3 cursor-pointer">
            <input
              type="checkbox"
              checked={formData.acceptPrivacy}
              onChange={(e) => updateField('acceptPrivacy', e.target.checked)}
              className="w-4 h-4 text-brand-600 border-neutral-300 rounded focus:ring-brand-500 focus:ring-2 mt-0.5"
            />
            <span className="text-sm text-neutral-700">
              J'accepte la{' '}
              <Link href="/privacy" className="text-brand-600 hover:text-brand-700 font-medium">
                politique de confidentialité
              </Link>
              {' '}*
            </span>
          </label>
          {errors.acceptPrivacy && (
            <p className="text-sm text-red-600 ml-7">{errors.acceptPrivacy}</p>
          )}
        </div>

        {/* Submit Button */}
        <LoadingButton
          type="submit"
          loading={loading}
          loadingText="Création..."
          className="w-full"
          size="lg"
        >
          Créer mon compte
        </LoadingButton>

        {/* Sign In Link */}
        <div className="text-center">
          <span className="text-sm text-neutral-600">
            Déjà un compte ?{' '}
          </span>
          <Link
            href="/login"
            className="text-sm font-medium text-brand-600 hover:text-brand-700 transition-colors"
          >
            Se connecter
          </Link>
        </div>
      </form>
    </AuthLayout>
  );
}
