'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { AuthLayout } from '@/components/auth/auth-layout';
import { PasswordField } from '@/components/auth/password-field';
import { LoadingButton } from '@/components/auth/loading-button';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useAuth } from '@/hooks/use-auth';

const validationRules = {
  password: [
    { required: true, message: 'Le mot de passe est requis' },
    { minLength: 8, message: 'Le mot de passe doit contenir au moins 8 caractères' },
    { 
      custom: (value: string) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/.test(value),
      message: 'Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial'
    }
  ],
  confirmPassword: [
    { required: true, message: 'La confirmation du mot de passe est requise' }
  ]
};

function ResetPasswordContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { updatePassword, loading } = useAuth();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isValidToken, setIsValidToken] = useState(true);
  const [passwordReset, setPasswordReset] = useState(false);
  const [errors, setErrors] = useState<{ password?: string; confirmPassword?: string }>({});

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    if (tokenParam) {
      setToken(tokenParam);
      // In a real app, you would validate the token with your API
      // For demo purposes, we'll accept any token
      setIsValidToken(true);
    } else {
      setIsValidToken(false);
    }
  }, [searchParams]);

  const validateForm = () => {
    const newErrors: { password?: string; confirmPassword?: string } = {};

    if (!password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    }

    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (!validateForm()) {
      return;
    }

    const { error } = await updatePassword(password);

    if (error) {
      setMessage({ type: 'error', text: error.message });
    } else {
      setMessage({ type: 'success', text: 'Votre mot de passe a été réinitialisé avec succès' });
      setPasswordReset(true);
    }
  };

  // Invalid token state
  if (!isValidToken) {
    return (
      <AuthLayout
        title="Lien invalide"
        subtitle="Ce lien de réinitialisation n'est pas valide"
        showBackToHome={false}
      >
        <div className="text-center space-y-6">
          {/* Error Icon */}
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <p className="text-neutral-700">
              Ce lien de réinitialisation est invalide ou a expiré.
            </p>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            <Link href="/forgot-password">
              <LoadingButton className="w-full">
                Demander un nouveau lien
              </LoadingButton>
            </Link>

            <Link
              href="/login"
              className="inline-flex items-center gap-2 text-sm text-brand-600 hover:text-brand-700 font-medium transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Retour à la connexion
            </Link>
          </div>
        </div>
      </AuthLayout>
    );
  }

  // Success state
  if (passwordReset) {
    return (
      <AuthLayout
        title="Mot de passe réinitialisé"
        subtitle="Votre mot de passe a été mis à jour avec succès"
        showBackToHome={false}
      >
        <div className="text-center space-y-6">
          {/* Success Icon */}
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <p className="text-neutral-700">
              Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.
            </p>
          </div>

          {/* Actions */}
          <Link href="/login">
            <LoadingButton className="w-full">
              Se connecter
            </LoadingButton>
          </Link>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Nouveau mot de passe"
      subtitle="Choisissez un nouveau mot de passe"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Message */}
        {message && (
          <div className={`p-4 rounded-lg flex items-center gap-3 ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
            )}
            <span className="text-sm">{message.text}</span>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-brand-50 rounded-lg p-4 border border-brand-100">
          <p className="text-sm text-brand-800">
            Choisissez un nouveau mot de passe sécurisé pour votre compte.
          </p>
        </div>

        {/* New Password Field */}
        <PasswordField
          id="password"
          name="password"
          label="Nouveau mot de passe"
          placeholder="••••••••"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          error={errors.password}
          showStrength={true}
          required
          autoComplete="new-password"
        />

        {/* Confirm Password Field */}
        <PasswordField
          id="confirmPassword"
          name="confirmPassword"
          label="Confirmer le mot de passe"
          placeholder="••••••••"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          error={errors.confirmPassword}
          required
          autoComplete="new-password"
        />

        {/* Submit Button */}
        <LoadingButton
          type="submit"
          loading={loading}
          loadingText="Réinitialisation..."
          className="w-full"
          size="lg"
        >
          Réinitialiser
        </LoadingButton>

        {/* Back to Login */}
        <div className="text-center">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-sm text-brand-600 hover:text-brand-700 font-medium transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Retour à la connexion
          </Link>
        </div>
      </form>
    </AuthLayout>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <AuthLayout
        title="Réinitialisation du mot de passe"
        subtitle="Chargement..."
      >
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-600"></div>
        </div>
      </AuthLayout>
    }>
      <ResetPasswordContent />
    </Suspense>
  );
}
