'use client';

import { motion } from 'framer-motion';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  MoreVertical, 
  Eye, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  ShoppingBag, 
  Star,
  Download,
  Upload,
  UserPlus,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign,
  Package
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { CustomerDetailModal } from '@/components/customers/customer-detail-modal';
import { CustomerFormModal } from '@/components/customers/customer-form-modal';

export default function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showFormModal, setShowFormModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<any>(null);

  // Données des clients - État initial vide
  const customers: any[] = [];

  const customerStats = [
    {
      title: 'Total Clients',
      value: '0',
      change: '0%',
      trend: 'neutral',
      icon: Users,
      color: 'blue'
    },
    {
      title: 'Nouveaux ce mois',
      value: '0',
      change: '0%',
      trend: 'neutral',
      icon: UserPlus,
      color: 'green'
    },
    {
      title: 'Clients actifs',
      value: '0',
      change: '0%',
      trend: 'neutral',
      icon: Activity,
      color: 'purple'
    },
    {
      title: 'Valeur moyenne',
      value: '0 DA',
      change: '0%',
      trend: 'neutral',
      icon: DollarSign,
      color: 'orange'
    }
  ];





  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) + ' DA';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const handleViewCustomer = (customer: any) => {
    setSelectedCustomer(customer);
    setShowDetailModal(true);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedCustomer(null);
  };

  const handleAddCustomer = () => {
    setEditingCustomer(null);
    setShowFormModal(true);
  };

  const handleEditCustomer = (customer: any) => {
    setEditingCustomer(customer);
    setShowFormModal(true);
  };

  const handleCloseFormModal = () => {
    setShowFormModal(false);
    setEditingCustomer(null);
  };

  const handleSaveCustomer = (customerData: any) => {
    // Ici, vous feriez l'appel API pour sauvegarder le client
    console.log('Sauvegarde du client:', customerData);

    // Simuler la mise à jour de la liste des clients
    // Dans un vrai projet, vous rechargeriez les données depuis l'API
    alert(`Client ${editingCustomer ? 'modifié' : 'ajouté'} avec succès !`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div>
          <h1 className="text-2xl font-bold text-neutral-900">Gestion des Clients</h1>
          <p className="text-neutral-600 mt-1">Gérez vos clients et analysez leur comportement</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" className="border-brand-200 text-brand-600 hover:bg-brand-50">
            <Download className="w-4 h-4 mr-2" />
            Exporter
          </Button>
          <Button variant="outline" className="border-brand-200 text-brand-600 hover:bg-brand-50">
            <Upload className="w-4 h-4 mr-2" />
            Importer
          </Button>
          <Button
            onClick={handleAddCustomer}
            className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Ajouter un client
          </Button>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {customerStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-neutral-600">{stat.title}</p>
                <p className="text-2xl font-bold text-neutral-900 mt-1">{stat.value}</p>
                <div className="flex items-center mt-2">
                  {stat.trend === 'up' ? (
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm font-medium ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change}
                  </span>
                </div>
              </div>
              <div className={`w-12 h-12 bg-${stat.color}-100 rounded-lg flex items-center justify-center`}>
                <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Filters and Search */}
      <motion.div
        className="bg-white rounded-xl shadow-sm border border-neutral-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <div className="p-6 border-b border-neutral-100">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Rechercher par nom ou email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>

            {/* Add Button */}
            <Button
              onClick={() => setShowFormModal(true)}
              className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Ajouter un client
            </Button>
          </div>
        </div>

        {/* Customer List */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-neutral-50">
              <tr>
                <th className="text-left py-3 px-6 text-sm font-medium text-neutral-600">
                  <input type="checkbox" className="rounded border-neutral-300" />
                </th>
                <th className="text-left py-3 px-6 text-sm font-medium text-neutral-600">Client</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-neutral-600">Contact</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-neutral-600">Localisation</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-neutral-600">Commandes</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-neutral-600">Total dépensé</th>
                <th className="text-left py-3 px-6 text-sm font-medium text-neutral-600">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-neutral-100">
              {filteredCustomers.map((customer, index) => (
                <motion.tr
                  key={customer.id}
                  className="hover:bg-neutral-50 transition-colors"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <td className="py-4 px-6">
                    <input type="checkbox" className="rounded border-neutral-300" />
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-brand-500 to-brand-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {customer.name.split(' ').map((n: string) => n[0]).join('')}
                      </div>
                      <div>
                        <p className="font-medium text-neutral-900">{customer.name}</p>
                        <p className="text-sm text-neutral-500">Inscrit le {formatDate(customer.joinDate)}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm text-neutral-600">
                        <Mail className="w-4 h-4" />
                        {customer.email}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-neutral-600">
                        <Phone className="w-4 h-4" />
                        {customer.phone}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2 text-sm text-neutral-600">
                      <MapPin className="w-4 h-4" />
                      {customer.location}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-center">
                      <p className="font-semibold text-neutral-900">{customer.totalOrders}</p>
                      <p className="text-xs text-neutral-500">Dernière: {formatDate(customer.lastOrder)}</p>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <p className="font-semibold text-neutral-900">{formatCurrency(customer.totalSpent)}</p>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-neutral-600 hover:text-brand-600"
                        onClick={() => handleViewCustomer(customer)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-neutral-600 hover:text-brand-600"
                        onClick={() => handleEditCustomer(customer)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-neutral-600 hover:text-red-600">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {filteredCustomers.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-neutral-700 mb-2">Aucun client trouvé</h3>
            <p className="text-neutral-500 mb-6">
              {searchTerm
                ? 'Aucun client ne correspond à vos critères de recherche.'
                : 'Vous n\'avez pas encore de clients. Commencez par ajouter votre premier client.'
              }
            </p>
            {!searchTerm && (
              <Button
                onClick={handleAddCustomer}
                className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Ajouter un client
              </Button>
            )}
          </div>
        )}
      </motion.div>

      {/* Customer Detail Modal */}
      <CustomerDetailModal
        customer={selectedCustomer}
        isOpen={showDetailModal}
        onClose={handleCloseDetailModal}
      />

      {/* Customer Form Modal */}
      <CustomerFormModal
        customer={editingCustomer}
        isOpen={showFormModal}
        onClose={handleCloseFormModal}
        onSave={handleSaveCustomer}
      />
    </div>
  );
}
