'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, XCircle } from 'lucide-react';

interface SupabaseStatus {
  url: string;
  keyConfigured: boolean;
  keyType: 'anon' | 'service_role' | 'unknown' | 'missing';
  connectionStatus: 'checking' | 'connected' | 'failed';
  error?: string;
}

export function SupabaseStatus() {
  const [status, setStatus] = useState<SupabaseStatus>({
    url: '',
    keyConfigured: false,
    keyType: 'missing',
    connectionStatus: 'checking'
  });

  useEffect(() => {
    const checkSupabaseConfig = () => {
      const url = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
      const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
      
      let keyType: SupabaseStatus['keyType'] = 'missing';
      
      if (key) {
        try {
          const payload = JSON.parse(atob(key.split('.')[1]));
          keyType = payload.role === 'anon' ? 'anon' : 
                   payload.role === 'service_role' ? 'service_role' : 'unknown';
        } catch {
          keyType = 'unknown';
        }
      }

      setStatus({
        url: url || 'Not configured',
        keyConfigured: !!key && key !== 'your-anon-key',
        keyType,
        connectionStatus: 'checking'
      });

      // Test connection
      if (url && key && key !== 'your-anon-key') {
        import('@/lib/supabase').then(({ supabase }) => {
          supabase.auth.getSession()
            .then(() => {
              setStatus(prev => ({ ...prev, connectionStatus: 'connected' }));
            })
            .catch((error) => {
              setStatus(prev => ({ 
                ...prev, 
                connectionStatus: 'failed',
                error: error.message 
              }));
            });
        });
      } else {
        setStatus(prev => ({ 
          ...prev, 
          connectionStatus: 'failed',
          error: 'Configuration incomplete' 
        }));
      }
    };

    checkSupabaseConfig();
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <h3 className="font-semibold text-sm mb-2 flex items-center gap-2">
        <AlertTriangle className="w-4 h-4 text-yellow-500" />
        Supabase Status
      </h3>
      
      <div className="space-y-2 text-xs">
        <div className="flex items-center gap-2">
          <span className="font-medium">URL:</span>
          <span className="text-gray-600 truncate">{status.url}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="font-medium">Key:</span>
          {status.keyConfigured ? (
            <span className={`flex items-center gap-1 ${
              status.keyType === 'anon' ? 'text-green-600' : 
              status.keyType === 'service_role' ? 'text-red-600' : 'text-yellow-600'
            }`}>
              {status.keyType === 'anon' ? <CheckCircle className="w-3 h-3" /> : <XCircle className="w-3 h-3" />}
              {status.keyType}
            </span>
          ) : (
            <span className="text-red-600 flex items-center gap-1">
              <XCircle className="w-3 h-3" />
              Not configured
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <span className="font-medium">Connection:</span>
          <span className={`flex items-center gap-1 ${
            status.connectionStatus === 'connected' ? 'text-green-600' : 
            status.connectionStatus === 'failed' ? 'text-red-600' : 'text-yellow-600'
          }`}>
            {status.connectionStatus === 'connected' ? <CheckCircle className="w-3 h-3" /> : 
             status.connectionStatus === 'failed' ? <XCircle className="w-3 h-3" /> : 
             <AlertTriangle className="w-3 h-3" />}
            {status.connectionStatus}
          </span>
        </div>
        
        {status.error && (
          <div className="text-red-600 text-xs mt-2 p-2 bg-red-50 rounded">
            {status.error}
          </div>
        )}
        
        {status.keyType === 'service_role' && (
          <div className="text-red-600 text-xs mt-2 p-2 bg-red-50 rounded">
            ⚠️ Using service_role key! Use anon key instead.
          </div>
        )}
      </div>
    </div>
  );
}
