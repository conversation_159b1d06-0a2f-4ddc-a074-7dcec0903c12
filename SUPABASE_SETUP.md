# Configuration Supabase pour Sharyou

## 🚀 Guide de Configuration

### 1. <PERSON><PERSON>er un Projet Supabase

1. Allez sur [https://supabase.com](https://supabase.com)
2. Créez un compte ou connectez-vous
3. Cliquez sur "New Project"
4. Choisissez votre organisation
5. Donnez un nom à votre projet (ex: "sharyou-auth")
6. Choi<PERSON><PERSON>z un mot de passe pour la base de données
7. Sélectionnez une région proche (ex: Europe West)
8. Cliquez sur "Create new project"

### 2. Obtenir les Clés API

Une fois votre projet créé :

1. Allez dans **Settings** > **API**
2. Copiez les valeurs suivantes :
   - **Project URL** (ex: `https://xyzcompany.supabase.co`)
   - **anon public** key (clé publique anonyme)

### 3. Configurer les Variables d'Environnement

Modifiez le fichier `.env.local` à la racine du projet :

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://votre-projet.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-cle-anon-publique
```

**⚠️ Important :** Remplacez les valeurs par vos vraies clés Supabase.

### 4. Configuration de l'Authentification

Dans votre tableau de bord Supabase :

1. Allez dans **Authentication** > **Settings**
2. Dans **Site URL**, ajoutez : `http://localhost:3002`
3. Dans **Redirect URLs**, ajoutez :
   - `http://localhost:3002/reset-password`
   - `http://localhost:3002/` (page d'accueil)

### 5. Configuration Email (Optionnel)

Pour les emails de récupération de mot de passe :

1. Allez dans **Authentication** > **Settings** > **SMTP Settings**
2. Configurez votre fournisseur d'email (Gmail, SendGrid, etc.)
3. Ou utilisez le service email intégré de Supabase

## 🔧 Fonctionnalités Disponibles

### ✅ Inscription
- Création de compte avec email/mot de passe
- Validation des données côté client
- Métadonnées utilisateur (prénom, nom)
- Email de confirmation automatique

### ✅ Connexion
- Authentification email/mot de passe
- Gestion des erreurs
- Redirection automatique vers le dashboard

### ✅ Récupération de Mot de Passe
- Envoi d'email de récupération
- Lien sécurisé de réinitialisation
- Interface de nouveau mot de passe

### ✅ Navigation Utilisateur
- Menu utilisateur dans la navigation
- Gestion de session
- Déconnexion sécurisée

## 🎨 Design

- **Palette bleue** cohérente avec Sharyou
- **Icône panier** intégrée dans tous les formulaires
- **Responsive design** mobile et desktop
- **Messages d'erreur** contextuels
- **États de chargement** avec animations

## 🔒 Sécurité

- **Validation côté client** et serveur
- **Tokens JWT** gérés par Supabase
- **Sessions sécurisées** avec refresh automatique
- **Protection CSRF** intégrée
- **Chiffrement** des mots de passe

## 🧪 Test de l'Application

1. Démarrez le serveur : `npm run dev`
2. Allez sur `http://localhost:3002/register`
3. Créez un compte de test
4. Vérifiez l'email (si configuré)
5. Connectez-vous sur `http://localhost:3002/login`
6. Accédez au dashboard

## 📱 Pages Disponibles

- `/login` - Connexion
- `/register` - Inscription  
- `/forgot-password` - Récupération de mot de passe
- `/reset-password` - Réinitialisation (avec token)
- `/` - Page d'accueil (après connexion)

## 🐛 Dépannage

### Erreur "Invalid API Key"
- Vérifiez que les clés dans `.env.local` sont correctes
- Redémarrez le serveur de développement

### Emails non reçus
- Vérifiez la configuration SMTP dans Supabase
- Regardez les logs dans Authentication > Logs

### Erreur de redirection
- Vérifiez les URLs autorisées dans Authentication > Settings

## 🚀 Déploiement

Pour la production :

1. Ajoutez votre domaine dans les Redirect URLs Supabase
2. Configurez les variables d'environnement sur votre plateforme
3. Activez RLS (Row Level Security) si nécessaire
4. Configurez les politiques de sécurité

---

**🎯 Votre système d'authentification Sharyou avec Supabase est maintenant prêt !**
