'use client';

import { useState, useEffect } from 'react';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  sku?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  images: string[];
  variants: Array<{
    id: string;
    name: string;
    values: string[];
  }>;
  isDraft: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: string;
  stock: string;
  sku: string;
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  images: File[];
  variants: Array<{
    id: string;
    name: string;
    values: string[];
  }>;
  isDraft: boolean;
  isActive: boolean;
}

export function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadProducts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const savedProducts = localStorage.getItem('sharyou_products');
      if (savedProducts) {
        const parsedProducts = JSON.parse(savedProducts).map((product: any) => ({
          ...product,
          createdAt: new Date(product.createdAt),
          updatedAt: new Date(product.updatedAt)
        }));
        setProducts(parsedProducts);
      }
    } catch (err) {
      setError('Erreur lors du chargement des produits');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveProductsToStorage = (updatedProducts: Product[]) => {
    try {
      localStorage.setItem('sharyou_products', JSON.stringify(updatedProducts));
    } catch (err) {
      console.error('Error saving products to storage:', err);
      throw new Error('Erreur lors de la sauvegarde');
    }
  };

  const addProduct = async (formData: ProductFormData): Promise<Product> => {
    setLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const imageUrls = formData.images.map((file, index) => 
        URL.createObjectURL(file)
      );

      const newProduct: Product = {
        id: Date.now().toString(),
        name: formData.name,
        description: formData.description,
        price: Number(formData.price),
        category: formData.category,
        stock: Number(formData.stock) || 0,
        sku: formData.sku || undefined,
        weight: Number(formData.weight) || undefined,
        dimensions: formData.dimensions.length || formData.dimensions.width || formData.dimensions.height
          ? {
              length: Number(formData.dimensions.length) || 0,
              width: Number(formData.dimensions.width) || 0,
              height: Number(formData.dimensions.height) || 0
            }
          : undefined,
        images: imageUrls,
        variants: formData.variants,
        isDraft: formData.isDraft,
        isActive: formData.isActive,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const updatedProducts = [...products, newProduct];
      setProducts(updatedProducts);
      saveProductsToStorage(updatedProducts);

      return newProduct;
    } catch (err) {
      setError('Erreur lors de l\'ajout du produit');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (id: string, formData: Partial<ProductFormData>): Promise<Product> => {
    setLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 800));

      const updatedProducts = products.map(product => {
        if (product.id === id) {
          const imageUrls = formData.images 
            ? formData.images.map(file => URL.createObjectURL(file))
            : product.images;

          return {
            ...product,
            ...Object.fromEntries(
              Object.entries(formData).filter(([_, value]) => value !== undefined)
            ),
            price: formData.price ? Number(formData.price) : product.price,
            stock: formData.stock ? Number(formData.stock) : product.stock,
            weight: formData.weight ? Number(formData.weight) : product.weight,
            images: imageUrls,
            updatedAt: new Date()
          };
        }
        return product;
      });

      setProducts(updatedProducts);
      saveProductsToStorage(updatedProducts);

      const updatedProduct = updatedProducts.find(p => p.id === id);
      if (!updatedProduct) {
        throw new Error('Produit non trouvé');
      }

      return updatedProduct;
    } catch (err) {
      setError('Erreur lors de la mise à jour du produit');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (id: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedProducts = products.filter(product => product.id !== id);
      setProducts(updatedProducts);
      saveProductsToStorage(updatedProducts);
    } catch (err) {
      setError('Erreur lors de la suppression du produit');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const toggleProductStatus = async (id: string): Promise<void> => {
    const product = products.find(p => p.id === id);
    if (!product) {
      throw new Error('Produit non trouvé');
    }

    await updateProduct(id, { isActive: !product.isActive } as any);
  };

  const getProductById = (id: string): Product | undefined => {
    return products.find(product => product.id === id);
  };

  const getProductsByCategory = (category: string): Product[] => {
    return products.filter(product => product.category === category);
  };

  const searchProducts = (query: string): Product[] => {
    const lowercaseQuery = query.toLowerCase();
    return products.filter(product =>
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.description.toLowerCase().includes(lowercaseQuery) ||
      product.category.toLowerCase().includes(lowercaseQuery) ||
      product.sku?.toLowerCase().includes(lowercaseQuery)
    );
  };

  const getProductStats = () => {
    const totalProducts = products.length;
    const activeProducts = products.filter(p => p.isActive && !p.isDraft).length;
    const draftProducts = products.filter(p => p.isDraft).length;
    const outOfStockProducts = products.filter(p => p.stock === 0).length;
    const lowStockProducts = products.filter(p => p.stock > 0 && p.stock < 10).length;

    return {
      total: totalProducts,
      active: activeProducts,
      drafts: draftProducts,
      outOfStock: outOfStockProducts,
      lowStock: lowStockProducts
    };
  };

  useEffect(() => {
    loadProducts();
  }, []);

  return {
    products,
    loading,
    error,
    addProduct,
    updateProduct,
    deleteProduct,
    toggleProductStatus,
    getProductById,
    getProductsByCategory,
    searchProducts,
    getProductStats,
    refreshProducts: loadProducts
  };
}
