'use client';

import { forwardRef, InputHTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface FormFieldProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  icon?: ReactNode;
  helperText?: string;
  showError?: boolean;
}

export const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  ({ 
    label, 
    error, 
    icon, 
    helperText, 
    showError = true, 
    className, 
    type = 'text',
    ...props 
  }, ref) => {
    const hasError = error && showError;

    return (
      <div className="space-y-2">
        {/* Label */}
        <label
          htmlFor={props.id || props.name}
          className="block text-sm font-medium text-neutral-700"
        >
          {label}
          {props.required && (
            <span className="text-brand-500 ml-1">*</span>
          )}
        </label>

        {/* Input Container */}
        <div className="relative">
          {/* Icon */}
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400">
              {icon}
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            type={type}
            className={cn(
              // Base styles
              "w-full px-4 py-3 rounded-lg border transition-all duration-200",
              "placeholder:text-neutral-400 text-neutral-900",
              "focus:outline-none focus:ring-2 focus:ring-brand-500/20",

              // Icon padding
              icon && "pl-11",

              // States
              hasError
                ? "border-red-300 focus:border-red-500 bg-red-50/50"
                : "border-neutral-200 focus:border-brand-500 bg-white hover:border-neutral-300",

              // Disabled state
              props.disabled && "opacity-50 cursor-not-allowed bg-neutral-50",

              className
            )}
            {...props}
          />

          {/* Password visibility toggle would go here if needed */}
        </div>

        {/* Helper Text or Error */}
        {(hasError || helperText) && (
          <div className="min-h-[1.25rem]">
            {hasError ? (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {error}
              </p>
            ) : helperText ? (
              <p className="text-sm text-neutral-500">
                {helperText}
              </p>
            ) : null}
          </div>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField';
