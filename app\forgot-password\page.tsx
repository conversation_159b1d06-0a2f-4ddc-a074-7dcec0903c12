'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Mail, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { AuthLayout } from '@/components/auth/auth-layout';
import { FormField } from '@/components/auth/form-field';
import { LoadingButton } from '@/components/auth/loading-button';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useAuth } from '@/hooks/use-auth';

const validationRules = {
  email: [
    { required: true, message: 'L\'email est requis' },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Format d\'email invalide' }
  ]
};

export default function ForgotPasswordPage() {
  const { resetPassword, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [emailSent, setEmailSent] = useState(false);
  const [error, setError] = useState('');

  const validateEmail = () => {
    if (!email) {
      setError('L\'email est requis');
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('Format d\'email invalide');
      return false;
    }
    setError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (!validateEmail()) {
      return;
    }

    const { error } = await resetPassword(email);

    if (error) {
      setMessage({ type: 'error', text: error.message });
    } else {
      setMessage({ type: 'success', text: 'Un email de récupération a été envoyé' });
      setEmailSent(true);
    }
  };

  if (emailSent) {
    return (
      <AuthLayout
        title="Email envoyé"
        subtitle="Vérifiez votre boîte de réception"
        showBackToHome={false}
      >
        <div className="text-center space-y-6">
          {/* Success Icon */}
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <p className="text-neutral-700">
              Un email de récupération a été envoyé à :
            </p>
            <p className="font-medium text-neutral-900">
              {email}
            </p>
          </div>

          {/* Instructions */}
          <div className="bg-neutral-50 rounded-lg p-4 text-left">
            <h3 className="font-medium text-neutral-900 mb-2">Étapes suivantes :</h3>
            <ol className="text-sm text-neutral-600 space-y-1 list-decimal list-inside">
              <li>Vérifiez votre boîte de réception</li>
              <li>Cliquez sur le lien dans l'email</li>
              <li>Créez un nouveau mot de passe</li>
              <li>Connectez-vous avec vos nouveaux identifiants</li>
            </ol>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            <LoadingButton
              onClick={() => setEmailSent(false)}
              variant="outline"
              className="w-full"
            >
              Renvoyer l'email
            </LoadingButton>

            <Link
              href="/login"
              className="inline-flex items-center gap-2 text-sm text-brand-600 hover:text-brand-700 font-medium transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Retour à la connexion
            </Link>
          </div>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout
      title="Mot de passe oublié"
      subtitle="Récupérez l'accès à votre compte"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Message */}
        {message && (
          <div className={`p-4 rounded-lg flex items-center gap-3 ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
            )}
            <span className="text-sm">{message.text}</span>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-brand-50 rounded-lg p-4 border border-brand-100">
          <p className="text-sm text-brand-800">
            Saisissez votre adresse email et nous vous enverrons un lien pour réinitialiser votre mot de passe.
          </p>
        </div>

        {/* Email Field */}
        <FormField
          id="email"
          name="email"
          type="email"
          label="Adresse email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          error={error}
          icon={<Mail className="w-5 h-5" />}
          required
          autoComplete="email"
        />

        {/* Submit Button */}
        <LoadingButton
          type="submit"
          loading={loading}
          loadingText="Envoi en cours..."
          className="w-full"
          size="lg"
        >
          Envoyer le lien
        </LoadingButton>

        {/* Back to Login */}
        <div className="text-center">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 text-sm text-brand-600 hover:text-brand-700 font-medium transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Retour à la connexion
          </Link>
        </div>
      </form>
    </AuthLayout>
  );
}
