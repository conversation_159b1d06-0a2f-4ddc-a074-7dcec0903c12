'use client';

import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Shield, 
  Lock, 
  Eye, 
  Database, 
  Bot, 
  Users, 
  Globe, 
  FileText,
  Settings,
  AlertTriangle,
  CheckCircle,
  Mail,
  Phone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function PrivacyPage() {
  const router = useRouter();

  const sections = [
    {
      id: 'introduction',
      title: '1. INTRODUCTION ET DÉFINITIONS',
      icon: Shield,
      content: [
        {
          subtitle: '1.1 Présentation',
          text: 'Sharyou, société de droit algérien immatriculée au Registre du Commerce sous le numéro [À compléter], ayant son siège social à [Adresse à compléter], <PERSON>gérie, s\'engage à protéger la confidentialité et la sécurité des données personnelles de tous ses utilisateurs.'
        },
        {
          subtitle: '1.2 Cadre Légal',
          text: 'La présente Politique de Confidentialité est établie en conformité avec :\n• La Loi n° 18-07 du 10 juin 2018 relative à la protection des personnes physiques dans le traitement des données à caractère personnel\n• La Constitution Algérienne (Article 46 - Vie privée et dignité humaine)\n• La Loi n° 18-05 du 10 mai 2018 relative au commerce électronique\n• Le Code Pénal Algérien concernant la protection de la vie privée'
        },
        {
          subtitle: '1.3 Définitions',
          text: '• Données personnelles : Toute information relative à une personne physique identifiée ou identifiable\n• Traitement : Toute opération effectuée sur des données personnelles\n• Responsable de traitement : Sharyou, qui détermine les finalités et moyens du traitement\n• Sous-traitant : Toute entité traitant des données pour le compte de Sharyou\n• Personne concernée : La personne physique dont les données sont traitées\n• Autorité Nationale : L\'Autorité de Protection des Données Personnelles (APDP)'
        }
      ]
    },
    {
      id: 'data-collection',
      title: '2. DONNÉES PERSONNELLES COLLECTÉES',
      icon: Database,
      content: [
        {
          subtitle: '2.1 Données d\'Identification',
          text: 'Nous collectons les données suivantes lors de votre inscription :\n• Nom et prénom\n• Adresse email\n• Numéro de téléphone\n• Date de naissance\n• Sexe\n• Nationalité\n• Adresse de résidence\n• Pièce d\'identité (pour la vérification)'
        },
        {
          subtitle: '2.2 Données Professionnelles (Vendeurs)',
          text: 'Pour les comptes vendeurs :\n• Raison sociale de l\'entreprise\n• Numéro d\'inscription au Registre du Commerce\n• Numéro d\'Identification Fiscale (NIF)\n• Adresse professionnelle\n• Secteur d\'activité\n• Informations bancaires pour les paiements'
        },
        {
          subtitle: '2.3 Données de Transaction',
          text: '• Historique des commandes\n• Montants des transactions\n• Modes de paiement utilisés\n• Adresses de livraison\n• Factures et reçus'
        },
        {
          subtitle: '2.4 Données Techniques',
          text: '• Adresse IP\n• Type de navigateur et système d\'exploitation\n• Pages visitées et durée des visites\n• Données de géolocalisation (avec consentement)\n• Cookies et technologies similaires\n• Logs de connexion'
        },
        {
          subtitle: '2.5 Données d\'Interaction',
          text: '• Avis et commentaires sur les produits\n• Messages dans le système de messagerie\n• Préférences et paramètres du compte\n• Données de support client\n• Interactions avec les outils d\'intelligence artificielle'
        }
      ]
    },
    {
      id: 'processing-purposes',
      title: '3. FINALITÉS DU TRAITEMENT',
      icon: Settings,
      content: [
        {
          subtitle: '3.1 Gestion des Comptes et Services',
          text: '• Création et gestion des comptes utilisateur\n• Authentification et sécurisation de l\'accès\n• Personnalisation de l\'expérience utilisateur\n• Gestion des profils vendeurs et acheteurs'
        },
        {
          subtitle: '3.2 Traitement des Commandes',
          text: '• Traitement et suivi des commandes\n• Gestion des paiements et de la facturation\n• Organisation de la livraison\n• Gestion des retours et remboursements\n• Service après-vente'
        },
        {
          subtitle: '3.3 Communication',
          text: '• Envoi de confirmations de commande\n• Notifications relatives au compte\n• Newsletter et communications marketing (avec consentement)\n• Support client et assistance technique\n• Enquêtes de satisfaction'
        },
        {
          subtitle: '3.4 Amélioration des Services',
          text: '• Analyse des performances de la plateforme\n• Développement de nouvelles fonctionnalités\n• Optimisation de l\'expérience utilisateur\n• Entraînement et amélioration des outils d\'intelligence artificielle\n• Études statistiques et recherches'
        },
        {
          subtitle: '3.5 Sécurité et Conformité',
          text: '• Prévention de la fraude et des abus\n• Détection d\'activités suspectes\n• Conformité aux obligations légales\n• Gestion des litiges et réclamations\n• Respect des décisions judiciaires'
        }
      ]
    },
    {
      id: 'legal-basis',
      title: '4. BASES LÉGALES DU TRAITEMENT',
      icon: FileText,
      content: [
        {
          subtitle: '4.1 Consentement',
          text: 'Le traitement est basé sur votre consentement libre, éclairé et spécifique pour :\n• Les communications marketing\n• L\'utilisation des données de géolocalisation\n• L\'amélioration des services IA par vos données d\'usage\n• Les cookies non essentiels'
        },
        {
          subtitle: '4.2 Exécution du Contrat',
          text: 'Le traitement est nécessaire à l\'exécution du contrat pour :\n• La création et gestion de votre compte\n• Le traitement des commandes\n• La livraison des produits\n• La facturation et les paiements'
        },
        {
          subtitle: '4.3 Obligations Légales',
          text: 'Le traitement est requis pour respecter nos obligations légales :\n• Conservation des factures (obligations fiscales)\n• Lutte contre le blanchiment d\'argent\n• Coopération avec les autorités judiciaires\n• Respect des réglementations sectorielles'
        },
        {
          subtitle: '4.4 Intérêts Légitimes',
          text: 'Le traitement est basé sur nos intérêts légitimes pour :\n• La sécurisation de la plateforme\n• La prévention de la fraude\n• L\'amélioration de nos services\n• Les analyses statistiques anonymisées'
        }
      ]
    },
    {
      id: 'ai-data',
      title: '5. INTELLIGENCE ARTIFICIELLE ET DONNÉES PERSONNELLES',
      icon: Bot,
      content: [
        {
          subtitle: '5.1 Utilisation des Données dans les Outils IA',
          text: 'Nos outils d\'intelligence artificielle peuvent traiter vos données pour :\n• Générer des recommandations personnalisées\n• Améliorer la pertinence des résultats de recherche\n• Optimiser l\'expérience utilisateur\n• Détecter la fraude et les comportements suspects\n• Fournir une assistance automatisée'
        },
        {
          subtitle: '5.2 Anonymisation et Agrégation',
          text: '• Les données utilisées pour l\'entraînement des modèles IA sont anonymisées\n• Nous utilisons des techniques d\'agrégation pour protéger l\'identité individuelle\n• Aucune donnée personnelle n\'est directement accessible aux modèles IA sans anonymisation'
        },
        {
          subtitle: '5.3 Consentement Spécifique pour l\'IA',
          text: 'Vous pouvez à tout moment :\n• Refuser l\'utilisation de vos données pour améliorer les services IA\n• Désactiver les recommandations personnalisées\n• Limiter l\'utilisation de l\'IA dans votre expérience utilisateur'
        },
        {
          subtitle: '5.4 Transparence Algorithmique',
          text: 'Nous nous engageons à :\n• Informer sur l\'utilisation de l\'IA dans nos services\n• Expliquer les logiques de recommandation utilisées\n• Permettre la contestation des décisions automatisées'
        }
      ]
    },
    {
      id: 'data-sharing',
      title: '6. PARTAGE ET TRANSFERT DES DONNÉES',
      icon: Globe,
      content: [
        {
          subtitle: '6.1 Partage Interne',
          text: 'Vos données peuvent être partagées entre les différents services de Sharyou pour les finalités décrites dans cette politique.'
        },
        {
          subtitle: '6.2 Partenaires et Prestataires',
          text: 'Nous partageons certaines données avec :\n• Les transporteurs pour la livraison\n• Les prestataires de paiement pour les transactions\n• Les prestataires techniques pour l\'hébergement et la maintenance\n• Les cabinets d\'audit et de conseil (sous accord de confidentialité)'
        },
        {
          subtitle: '6.3 Autorités Publiques',
          text: 'Nous pouvons communiquer vos données aux autorités compétentes :\n• Sur réquisition judiciaire\n• Pour respecter une obligation légale\n• Dans le cadre de la lutte contre la fraude\n• Pour protéger nos droits et intérêts légitimes'
        },
        {
          subtitle: '6.4 Transferts Internationaux',
          text: 'Conformément à la Loi 18-07, tout transfert de données personnelles vers un pays tiers nécessite :\n• L\'autorisation préalable de l\'Autorité Nationale de Protection des Données\n• La garantie d\'un niveau de protection adéquat\n• Votre consentement explicite le cas échéant'
        }
      ]
    },
    {
      id: 'security',
      title: '7. SÉCURITÉ DES DONNÉES',
      icon: Lock,
      content: [
        {
          subtitle: '7.1 Mesures Techniques',
          text: 'Nous mettons en œuvre :\n• Chiffrement des données sensibles (SSL/TLS)\n• Contrôles d\'accès stricts et authentification forte\n• Surveillance continue des systèmes\n• Sauvegarde régulière des données\n• Protection contre les cyberattaques'
        },
        {
          subtitle: '7.2 Mesures Organisationnelles',
          text: '• Formation du personnel sur la protection des données\n• Audits réguliers de sécurité\n• Procédures de gestion des incidents\n• Clauses de confidentialité dans tous les contrats\n• Politique de gestion des accès'
        },
        {
          subtitle: '7.3 Hébergement Sécurisé',
          text: 'Conformément à la réglementation algérienne :\n• Toutes les données sont hébergées sur le territoire algérien\n• Les centres de données respectent les normes de sécurité internationales\n• Les prestataires d\'hébergement sont certifiés et audités'
        },
        {
          subtitle: '7.4 Gestion des Incidents',
          text: 'En cas de violation de données :\n• Notification à l\'Autorité Nationale dans les 72 heures\n• Information des personnes concernées si le risque est élevé\n• Mesures correctives immédiates\n• Enquête approfondie et rapport détaillé'
        }
      ]
    },
    {
      id: 'rights',
      title: '8. VOS DROITS',
      icon: Users,
      content: [
        {
          subtitle: '8.1 Droit d\'Information',
          text: 'Vous avez le droit d\'obtenir :\n• La confirmation du traitement de vos données\n• Les finalités du traitement\n• Les destinataires de vos données\n• La durée de conservation\n• L\'origine des données'
        },
        {
          subtitle: '8.2 Droit d\'Accès',
          text: 'Vous pouvez demander :\n• Une copie de toutes vos données personnelles\n• Les détails sur le traitement effectué\n• Les garanties de transfert le cas échéant'
        },
        {
          subtitle: '8.3 Droit de Rectification',
          text: 'Vous pouvez :\n• Corriger les données inexactes\n• Compléter les données incomplètes\n• Mettre à jour vos informations'
        },
        {
          subtitle: '8.4 Droit d\'Effacement',
          text: 'Vous pouvez demander la suppression de vos données dans les cas suivants :\n• Les données ne sont plus nécessaires\n• Vous retirez votre consentement\n• Le traitement est illicite\n• Obligation légale de suppression'
        },
        {
          subtitle: '8.5 Droit à la Limitation',
          text: 'Vous pouvez demander la limitation du traitement si :\n• Vous contestez l\'exactitude des données\n• Le traitement est illicite mais vous ne voulez pas l\'effacement\n• Nous n\'avons plus besoin des données mais vous en avez besoin pour un recours\n• Vous vous opposez au traitement'
        },
        {
          subtitle: '8.6 Droit à la Portabilité',
          text: 'Pour les données traitées automatiquement avec votre consentement, vous pouvez :\n• Recevoir vos données dans un format structuré\n• Transmettre ces données à un autre responsable de traitement'
        },
        {
          subtitle: '8.7 Droit d\'Opposition',
          text: 'Vous pouvez vous opposer :\n• Au traitement à des fins de marketing direct\n• Au traitement basé sur l\'intérêt légitime\n• Au profilage automatisé'
        },
        {
          subtitle: '8.8 Exercice des Droits',
          text: 'Pour exercer vos droits :\n• Connectez-vous à votre compte et accédez aux paramètres de confidentialité\n• Contactez-nous à <EMAIL>\n• Écrivez-nous à notre adresse postale\n• Utilisez le formulaire dédié sur notre site web\n\nDélai de réponse : Nous nous engageons à répondre dans un délai de 30 jours maximum.'
        }
      ]
    },
    {
      id: 'cookies',
      title: '9. COOKIES ET TECHNOLOGIES SIMILAIRES',
      icon: Settings,
      content: [
        {
          subtitle: '9.1 Types de Cookies Utilisés',
          text: '• Cookies essentiels : Nécessaires au fonctionnement de la plateforme\n• Cookies de performance : Analyse de l\'utilisation du site\n• Cookies fonctionnels : Mémorisation de vos préférences\n• Cookies publicitaires : Personnalisation des annonces (avec consentement)'
        },
        {
          subtitle: '9.2 Gestion des Cookies',
          text: 'Vous pouvez :\n• Configurer vos préférences dans les paramètres de cookies\n• Modifier les paramètres de votre navigateur\n• Supprimer les cookies existants\n• Refuser les cookies non essentiels'
        },
        {
          subtitle: '9.3 Conséquences du Refus',
          text: 'Le refus de certains cookies peut limiter :\n• Les fonctionnalités de personnalisation\n• Les recommandations produits\n• L\'analyse de performance\n• L\'expérience utilisateur optimisée'
        }
      ]
    },
    {
      id: 'retention',
      title: '10. CONSERVATION DES DONNÉES',
      icon: Database,
      content: [
        {
          subtitle: '10.1 Durées de Conservation',
          text: '• Données de compte actif : Pendant la durée de vie du compte + 1 an\n• Données de transaction : 10 ans (obligations fiscales)\n• Données de communication : 5 ans\n• Logs de connexion : 1 an\n• Données marketing : Jusqu\'au retrait du consentement + 3 ans'
        },
        {
          subtitle: '10.2 Critères de Détermination',
          text: 'Les durées sont déterminées selon :\n• Les obligations légales applicables\n• La nécessité pour les finalités du traitement\n• Les délais de prescription légaux\n• Vos intérêts et droits fondamentaux'
        },
        {
          subtitle: '10.3 Suppression Automatique',
          text: 'Nous avons mis en place des procédures automatiques pour :\n• Supprimer les données expirées\n• Anonymiser les données anciennes\n• Purger les systèmes de sauvegarde\n• Notifier les sous-traitants des suppressions'
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Header */}
      <div className="bg-white border-b border-neutral-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="flex items-center gap-2 text-neutral-600 hover:text-neutral-900"
            >
              <ArrowLeft className="w-4 h-4" />
              Retour
            </Button>
            <div className="h-6 w-px bg-neutral-300" />
            <div>
              <h1 className="text-2xl font-bold text-neutral-900">Politique de Confidentialité</h1>
              <p className="text-sm text-neutral-600 mt-1">Dernière mise à jour : 26 juin 2025</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction */}
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-brand-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Shield className="w-6 h-6 text-brand-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 mb-2">Protection de vos données personnelles</h2>
              <p className="text-neutral-600 leading-relaxed">
                Chez Sharyou, nous accordons la plus haute importance à la protection de vos données personnelles. 
                Cette politique détaille comment nous collectons, utilisons et protégeons vos informations, 
                en conformité avec la législation algérienne sur la protection des données.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Key Principles */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <Lock className="w-5 h-5 text-green-600" />
            </div>
            <h3 className="font-semibold text-neutral-900 mb-2">Sécurité Renforcée</h3>
            <p className="text-sm text-neutral-600">Chiffrement et protection avancée de toutes vos données personnelles</p>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <Eye className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="font-semibold text-neutral-900 mb-2">Transparence Totale</h3>
            <p className="text-sm text-neutral-600">Information claire sur l'utilisation de vos données et vos droits</p>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <CheckCircle className="w-5 h-5 text-purple-600" />
            </div>
            <h3 className="font-semibold text-neutral-900 mb-2">Conformité Légale</h3>
            <p className="text-sm text-neutral-600">Respect strict de la Loi 18-07 algérienne sur la protection des données</p>
          </div>
        </motion.div>

        {/* Sections */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={section.id}
              className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="bg-gradient-to-r from-brand-50 to-accent-50 px-6 py-4 border-b border-neutral-100">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-brand-500 rounded-lg flex items-center justify-center">
                    <section.icon className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-900">{section.title}</h3>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                {section.content.map((item, itemIndex) => (
                  <div key={itemIndex}>
                    {item.subtitle && (
                      <h4 className="font-semibold text-neutral-900 mb-2">{item.subtitle}</h4>
                    )}
                    <div className="text-neutral-700 leading-relaxed whitespace-pre-line">
                      {item.text}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Contact and Complaints */}
        <motion.div
          className="bg-gradient-to-r from-brand-500 to-brand-700 rounded-xl text-white p-6 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.8 }}
        >
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Contact et Réclamations
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-brand-100 mb-2">Contact Sharyou</h4>
              <div className="space-y-1 text-sm text-brand-100">
                <p>Email : <EMAIL></p>
                <p>DPD : <EMAIL></p>
                <p>Téléphone : [Numéro dédié protection des données]</p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-brand-100 mb-2">Autorité de Contrôle</h4>
              <div className="space-y-1 text-sm text-brand-100">
                <p>Autorité de Protection des Données Personnelles (APDP)</p>
                <p>Email : [Contact officiel de l'APDP]</p>
                <p>Site web : [Site officiel de l'APDP]</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Data Protection Officer */}
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6 mt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.9 }}
        >
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-neutral-900 mb-2">Délégué à la Protection des Données</h3>
              <p className="text-neutral-600 mb-4">
                Sharyou a désigné un Délégué à la Protection des Données (DPD) conformément à la Loi 18-07.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-neutral-900 mb-2">Missions du DPD</h4>
                  <ul className="text-sm text-neutral-600 space-y-1">
                    <li>• Conseil et accompagnement sur la conformité</li>
                    <li>• Point de contact avec l'Autorité Nationale</li>
                    <li>• Formation et sensibilisation du personnel</li>
                    <li>• Contrôle du respect de la réglementation</li>
                    <li>• Gestion des réclamations relatives aux données</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-neutral-900 mb-2">Contact du DPD</h4>
                  <div className="text-sm text-neutral-600 space-y-1">
                    <p>Email : <EMAIL></p>
                    <p>Adresse postale : [Adresse complète]</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Legal Information */}
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6 mt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.0 }}
        >
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">Informations Légales</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-neutral-900 mb-2">Responsable de Traitement</h4>
              <div className="text-sm text-neutral-600 space-y-1">
                <p><strong>SHARYOU</strong></p>
                <p>Forme juridique : [SARL/SPA/EURL]</p>
                <p>Siège social : [Adresse complète]</p>
                <p>RC : [Numéro RC]</p>
                <p>NIF : [Numéro d'Identification Fiscale]</p>
                <p>Représentant légal : [Nom du dirigeant]</p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-neutral-900 mb-2">Références Légales</h4>
              <div className="text-sm text-neutral-600 space-y-1">
                <p>• Loi n° 18-07 du 10 juin 2018</p>
                <p>• Décret exécutif n° 20-05 du 12 janvier 2020</p>
                <p>• Constitution Algérienne (Article 46)</p>
                <p>• Code Pénal Algérien (Articles 303 bis 1 à 303 bis 8)</p>
              </div>
            </div>
          </div>

          <div className="mt-6 pt-6 border-t border-neutral-100">
            <h4 className="font-semibold text-neutral-900 mb-2">Certifications et Audits</h4>
            <p className="text-sm text-neutral-600">
              Sharyou s'engage à maintenir la certification ISO 27001 pour la sécurité de l'information,
              effectuer des audits annuels de conformité, publier un rapport annuel de transparence et
              coopérer avec les contrôles de l'Autorité Nationale.
            </p>
          </div>

          <div className="mt-4 pt-4 border-t border-neutral-100">
            <p className="text-sm text-neutral-500">
              <strong>Cette Politique de Confidentialité est effective à compter du 26 juin 2025.</strong><br />
              Pour toute question concernant cette politique, contactez-nous à <EMAIL>
            </p>
          </div>
        </motion.div>

        {/* Important Notice */}
        <motion.div
          className="bg-amber-50 border border-amber-200 rounded-xl p-6 mt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.1 }}
        >
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-semibold text-amber-900 mb-2">Mineurs</h4>
              <p className="text-sm text-amber-800">
                L'utilisation de la plateforme Sharyou est réservée aux personnes âgées de 18 ans révolus.
                Pour les mineurs de 13 à 18 ans, l'utilisation nécessite le consentement préalable du représentant légal
                et une supervision parentale continue.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
