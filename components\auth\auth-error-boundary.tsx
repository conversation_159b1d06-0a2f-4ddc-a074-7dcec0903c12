'use client';

import React, { Component, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { clearAuthData } from '@/lib/auth-utils';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Vérifier si c'est une erreur d'authentification Supabase
    const isAuthError = error.message.includes('Invalid Refresh Token') ||
                       error.message.includes('Refresh Token Not Found') ||
                       error.message.includes('AuthApiError');
    
    if (isAuthError) {
      return { hasError: true, error };
    }
    
    // Pour les autres erreurs, ne pas intercepter
    throw error;
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Auth error caught by boundary:', error, errorInfo);
    
    // Nettoyer automatiquement les données d'auth corrompues
    clearAuthData().catch(console.error);
  }

  handleRetry = async () => {
    try {
      // Nettoyer les données d'auth
      await clearAuthData();
      
      // Réinitialiser l'état d'erreur
      this.setState({ hasError: false, error: undefined });
      
      // Recharger la page pour redémarrer l'app
      window.location.reload();
    } catch (error) {
      console.error('Error during retry:', error);
      window.location.href = '/login';
    }
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-neutral-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            
            <h1 className="text-xl font-bold text-neutral-900 mb-4">
              Problème d'authentification
            </h1>
            
            <p className="text-neutral-600 mb-6 leading-relaxed">
              Votre session a expiré ou est corrompue. Nous allons nettoyer les données 
              d'authentification et vous rediriger vers la page de connexion.
            </p>
            
            <div className="space-y-3">
              <Button
                onClick={this.handleRetry}
                className="w-full bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Réessayer
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.location.href = '/login'}
                className="w-full"
              >
                Aller à la connexion
              </Button>
            </div>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6 text-left">
                <summary className="text-sm text-neutral-500 cursor-pointer">
                  Détails de l'erreur (dev)
                </summary>
                <pre className="mt-2 text-xs text-red-600 bg-red-50 p-3 rounded overflow-auto">
                  {this.state.error.message}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
