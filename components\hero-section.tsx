'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Play, ShoppingBag, Users, Clock, TrendingUp } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import { SharyouIcon } from '@/components/icons/sharyou-icon';
import { useAuth } from '@/hooks/use-auth';

export function HeroSection() {
  const { t, isRTL } = useLanguage();
  const { user } = useAuth();
  const router = useRouter();
  const [currentStats, setCurrentStats] = useState({
    stores: 0,
    time: 0,
    success: 0
  });

  useEffect(() => {
    // Animate statistics
    const targetStats = { stores: 1247, time: 4.5, success: 96 };
    const duration = 2000;
    const steps = 60;
    const stepDuration = duration / steps;

    let step = 0;
    const interval = setInterval(() => {
      step++;
      const progress = step / steps;
      
      setCurrentStats({
        stores: Math.floor(targetStats.stores * progress),
        time: Math.round(targetStats.time * progress * 10) / 10,
        success: Math.floor(targetStats.success * progress)
      });

      if (step >= steps) {
        clearInterval(interval);
        setCurrentStats(targetStats);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, []);

  const handleGetStarted = () => {
    if (user) {
      router.push('/dashboard');
    } else {
      router.push('/register');
    }
  };

  const handleWatchDemo = () => {
    // For now, scroll to features section
    const element = document.getElementById('ai-builder');
    if (element) {
      const headerOffset = 80;
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center pt-16 bg-gradient-to-br from-brand-50/30 via-white to-accent-50/20">
      {/* Simplified Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-500 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-500 rounded-full filter blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          {/* Content */}
          <div className="space-y-8 max-w-4xl mx-auto">
            <div className="space-y-6">
              <div className="inline-flex items-center gap-3 px-5 py-2.5 bg-white/90 backdrop-blur-sm rounded-full border border-brand-200 mx-auto shadow-lg shadow-brand-500/10">
                <SharyouIcon size={20} />
                <span className="text-sm font-medium text-brand-700">
                  {t('hero.badge')}
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold text-neutral-900 leading-tight tracking-tight">
                {t('hero.title')}
                <span className="block bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent mt-2">
                  {t('hero.subtitle')}
                </span>
              </h1>

              <p className="text-lg md:text-xl text-neutral-600 leading-relaxed max-w-3xl mx-auto font-light">
                {t('hero.description')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                onClick={handleGetStarted}
                className="bg-gradient-to-r from-brand-500 to-brand-700 hover:from-brand-600 hover:to-brand-800 text-white px-12 py-4 text-lg font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl hover:shadow-brand-500/25 transform hover:scale-105"
              >
                {user ? 'Accéder au Dashboard' : 'Commencer Gratuitement'}
              </Button>

              <Button
                variant="outline"
                size="lg"
                onClick={handleWatchDemo}
                className="px-12 py-4 text-lg font-semibold rounded-xl border-2 border-neutral-300 hover:border-brand-500 hover:text-brand-600 hover:bg-brand-50 transition-all duration-200"
              >
                <Play className="h-5 w-5 mr-2" />
                {t('hero.cta2')}
              </Button>
            </div>

            {/* Simplified Trust Badges */}
            <div className="flex flex-wrap justify-center gap-6 py-6">
              <div className="flex items-center gap-2 text-sm text-neutral-600 bg-white/80 px-4 py-2.5 rounded-full shadow-sm border border-neutral-100">
                <SharyouIcon size={16} />
                {t('hero.trustBadges.aiPowered')}
              </div>
              <div className="flex items-center gap-2 text-sm text-neutral-600 bg-white/80 px-4 py-2.5 rounded-full shadow-sm border border-neutral-100">
                <ShoppingBag className="h-4 w-4 text-brand-500" />
                {t('hero.trustBadges.localPayments')}
              </div>
              <div className="flex items-center gap-2 text-sm text-neutral-600 bg-white/80 px-4 py-2.5 rounded-full shadow-sm border border-neutral-100">
                <Users className="h-4 w-4 text-accent-500" />
                {t('hero.trustBadges.multiLanguage')}
              </div>
            </div>

            {/* Key Statistics - Simplified */}
            <div className="grid grid-cols-3 gap-6 pt-8 mt-8 border-t border-neutral-200/50 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-display font-bold text-neutral-900 mb-1">
                  {currentStats.stores.toLocaleString()}+
                </div>
                <p className="text-sm text-neutral-600 font-medium">{t('hero.stats.stores')}</p>
              </div>

              <div className="text-center">
                <div className="text-2xl md:text-3xl font-display font-bold text-brand-600 mb-1">
                  {currentStats.time}min
                </div>
                <p className="text-sm text-neutral-600 font-medium">{t('hero.stats.time')}</p>
              </div>

              <div className="text-center">
                <div className="text-2xl md:text-3xl font-display font-bold text-accent-600 mb-1">
                  {currentStats.success}%
                </div>
                <p className="text-sm text-neutral-600 font-medium">{t('hero.stats.success')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}