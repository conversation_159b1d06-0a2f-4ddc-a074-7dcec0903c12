'use client';

import { forwardRef, InputHTMLAttributes, useState } from 'react';
import { Eye, EyeOff, Lock } from 'lucide-react';
import { FormField } from './form-field';
import { cn } from '@/lib/utils';

interface PasswordFieldProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label: string;
  error?: string;
  showStrength?: boolean;
  helperText?: string;
  showError?: boolean;
}

interface PasswordStrength {
  score: number;
  feedback: string[];
  isValid: boolean;
}

function calculatePasswordStrength(password: string): PasswordStrength {
  let score = 0;
  const feedback: string[] = [];

  if (password.length === 0) {
    return { score: 0, feedback: [], isValid: false };
  }

  // Length check
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Au moins 8 caractères');
  }

  // Lowercase check
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Une lettre minuscule');
  }

  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Une lettre majuscule');
  }

  // Number check
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Un chiffre');
  }

  // Special character check
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Un caractère spécial');
  }

  return {
    score,
    feedback,
    isValid: score >= 4
  };
}

export const PasswordField = forwardRef<HTMLInputElement, PasswordFieldProps>(
  ({ 
    label, 
    error, 
    showStrength = false, 
    helperText, 
    showError = true,
    value,
    onChange,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const [strength, setStrength] = useState<PasswordStrength>({ score: 0, feedback: [], isValid: false });

    const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newPassword = e.target.value;
      if (showStrength) {
        setStrength(calculatePasswordStrength(newPassword));
      }
      onChange?.(e);
    };

    const getStrengthColor = (score: number) => {
      if (score <= 1) return 'bg-red-500';
      if (score <= 2) return 'bg-orange-500';
      if (score <= 3) return 'bg-yellow-500';
      if (score <= 4) return 'bg-green-500';
      return 'bg-green-600';
    };

    const getStrengthText = (score: number) => {
      if (score <= 1) return 'Très faible';
      if (score <= 2) return 'Faible';
      if (score <= 3) return 'Moyen';
      if (score <= 4) return 'Fort';
      return 'Très fort';
    };

    return (
      <div className="space-y-2">
        {/* Label */}
        <label 
          htmlFor={props.id || props.name}
          className="block text-sm font-medium text-neutral-700"
        >
          {label}
          {props.required && (
            <span className="text-brand-500 ml-1">*</span>
          )}
        </label>

        {/* Input Container */}
        <div className="relative">
          {/* Lock Icon */}
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400">
            <Lock className="w-5 h-5" />
          </div>

          {/* Input */}
          <input
            ref={ref}
            type={showPassword ? 'text' : 'password'}
            value={value}
            onChange={handlePasswordChange}
            className={cn(
              "w-full pl-11 pr-12 py-3 rounded-lg border transition-all duration-200",
              "placeholder:text-neutral-400 text-neutral-900",
              "focus:outline-none focus:ring-2 focus:ring-brand-500/20",
              error && showError
                ? "border-red-300 focus:border-red-500 bg-red-50/50" 
                : "border-neutral-200 focus:border-brand-500 bg-white hover:border-neutral-300",
              props.disabled && "opacity-50 cursor-not-allowed bg-neutral-50"
            )}
            {...props}
          />

          {/* Toggle Password Visibility */}
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600 transition-colors"
          >
            {showPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Password Strength Indicator */}
        {showStrength && value && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-neutral-200 rounded-full h-2">
                <div 
                  className={cn(
                    "h-2 rounded-full transition-all duration-300",
                    getStrengthColor(strength.score)
                  )}
                  style={{ width: `${(strength.score / 5) * 100}%` }}
                />
              </div>
              <span className="text-xs font-medium text-neutral-600">
                {getStrengthText(strength.score)}
              </span>
            </div>
            
            {strength.feedback.length > 0 && (
              <div className="text-xs text-neutral-500">
                Manque : {strength.feedback.join(', ')}
              </div>
            )}
          </div>
        )}

        {/* Error or Helper Text */}
        {((error && showError) || helperText) && (
          <div className="min-h-[1.25rem]">
            {error && showError ? (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {error}
              </p>
            ) : helperText ? (
              <p className="text-sm text-neutral-500">
                {helperText}
              </p>
            ) : null}
          </div>
        )}
      </div>
    );
  }
);

PasswordField.displayName = 'PasswordField';
