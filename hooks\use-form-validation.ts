'use client';

import { useState, useCallback } from 'react';
import { FormValidationError, ValidationRules } from '@/types/auth';

export function useFormValidation<T extends Record<string, any>>(
  initialData: T,
  validationRules: ValidationRules
) {
  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<FormValidationError[]>([]);
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);

  // Validate a single field
  const validateField = useCallback((fieldName: keyof T, value: any): string | null => {
    const rules = validationRules[fieldName as string];
    if (!rules) return null;

    for (const rule of rules) {
      // Required validation
      if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        return rule.message;
      }

      // Skip other validations if field is empty and not required
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        continue;
      }

      // Min length validation
      if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
        return rule.message;
      }

      // Max length validation
      if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
        return rule.message;
      }

      // Pattern validation
      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        return rule.message;
      }

      // Custom validation
      if (rule.custom && !rule.custom(value)) {
        return rule.message;
      }
    }

    return null;
  }, [validationRules]);

  // Validate all fields
  const validateAll = useCallback((): boolean => {
    const newErrors: FormValidationError[] = [];

    Object.keys(validationRules).forEach(fieldName => {
      const error = validateField(fieldName as keyof T, data[fieldName as keyof T]);
      if (error) {
        newErrors.push({ field: fieldName, message: error });
      }
    });

    setErrors(newErrors);
    return newErrors.length === 0;
  }, [data, validateField, validationRules]);

  // Update field value
  const updateField = useCallback((fieldName: keyof T, value: any) => {
    setData(prev => ({ ...prev, [fieldName]: value }));
    
    // Mark field as touched
    setTouched(prev => ({ ...prev, [fieldName]: true }));

    // Validate field if it's been touched
    if (touched[fieldName]) {
      const error = validateField(fieldName, value);
      setErrors(prev => {
        const filtered = prev.filter(e => e.field !== fieldName);
        return error ? [...filtered, { field: fieldName as string, message: error }] : filtered;
      });
    }
  }, [touched, validateField]);

  // Mark field as touched
  const touchField = useCallback((fieldName: keyof T) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    
    // Validate the field when touched
    const error = validateField(fieldName, data[fieldName]);
    if (error) {
      setErrors(prev => {
        const filtered = prev.filter(e => e.field !== fieldName);
        return [...filtered, { field: fieldName as string, message: error }];
      });
    }
  }, [data, validateField]);

  // Get error for specific field
  const getFieldError = useCallback((fieldName: keyof T): string | undefined => {
    const error = errors.find(e => e.field === fieldName);
    return error?.message;
  }, [errors]);

  // Check if field has error
  const hasFieldError = useCallback((fieldName: keyof T): boolean => {
    return errors.some(e => e.field === fieldName);
  }, [errors]);

  // Reset form
  const reset = useCallback(() => {
    setData(initialData);
    setErrors([]);
    setTouched({} as Record<keyof T, boolean>);
  }, [initialData]);

  // Clear errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  return {
    data,
    errors,
    touched,
    updateField,
    touchField,
    validateAll,
    validateField,
    getFieldError,
    hasFieldError,
    reset,
    clearErrors,
    isValid: errors.length === 0
  };
}
