'use client';

import {
  Facebook,
  Instagram,
  Linkedin,
  Mail,
  MapPin,
  Youtube,
  Shield
} from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';
import { SharyouIcon } from '@/components/icons/sharyou-icon';

export function Footer() {
  const { t, isRTL } = useLanguage();

  const socialLinks = [
    { icon: Facebook, href: '#', label: t('footer.social.facebook') },
    { icon: Instagram, href: '#', label: t('footer.social.instagram') },
    { icon: Linkedin, href: '#', label: t('footer.social.linkedin') },
    { icon: Youtube, href: '#', label: t('footer.social.youtube') },
  ];

  return (
    <footer className="bg-neutral-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer */}
        <div className="py-16">
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-2 space-y-6">
              <div className="flex items-center gap-3">
                <SharyouIcon size={40} />
                <span className="text-2xl font-display font-bold">Sharyou</span>
              </div>

              <p className="text-neutral-300 leading-relaxed max-w-md font-light">
                {t('footer.description')}
              </p>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-brand-400" />
                  <span className="text-neutral-300">{t('footer.contact.email')}</span>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-brand-400" />
                  <span className="text-neutral-300">{t('footer.contact.address')}</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white">Liens Rapides</h3>
              <ul className="space-y-3">
                <li>
                  <a href="#ai-builder" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    {t('footer.links.features')}
                  </a>
                </li>
                <li>
                  <a href="#why-choose" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    {t('footer.links.pricing')}
                  </a>
                </li>
                <li>
                  <a href="#testimonials" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    Témoignages
                  </a>
                </li>
                <li>
                  <a href="#footer" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    {t('footer.links.contact')}
                  </a>
                </li>
              </ul>
            </div>

            {/* Support & Legal */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white">Support</h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    {t('footer.links.help')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    {t('footer.links.privacy')}
                  </a>
                </li>
                <li>
                  <a href="#" className="text-neutral-300 hover:text-brand-400 transition-colors duration-200">
                    {t('footer.links.terms')}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Social Links */}
          <div className="mt-12 pt-8 border-t border-neutral-800">
            <div className="flex justify-center gap-6">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="w-12 h-12 bg-neutral-800 rounded-xl flex items-center justify-center hover:bg-gradient-to-br hover:from-brand-500 hover:to-accent-500 transition-all duration-300 transform hover:scale-110"
                  aria-label={social.label}
                  title={social.label}
                >
                  <social.icon className="h-5 w-5" />
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-8 border-t border-neutral-800">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5 text-brand-400" />
              <span className="text-neutral-400 text-sm font-medium">Plateforme Sécurisée & Certifiée</span>
            </div>

            <p className="text-neutral-400 text-sm text-center">
              {t('footer.copyright')}
            </p>

            <div className="flex items-center gap-2 text-xs text-neutral-500 font-medium">
              <span>🇩🇿</span>
              <span>Fait en Algérie</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}