'use client';

import { motion } from 'framer-motion';
import { ShoppingCart, Search, Filter, MoreHorizontal, Eye, Download, Inbox, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';

const orders: any[] = [];

export default function OrdersPage() {
  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <motion.div
        className="flex justify-end mb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Button variant="outline" className="border-brand-200 text-brand-600 hover:bg-brand-50">
          <Download className="w-4 h-4 mr-2" />
          Exporter
        </Button>
      </motion.div>

      {/* Filters */}
      <motion.div
        className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Rechercher une commande..."
                className="w-full pl-10 pr-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all"
              />
            </div>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" className="flex items-center gap-2 border-neutral-200 hover:border-brand-300">
              <Filter className="w-4 h-4" />
              Filtres
            </Button>
            <select className="px-4 py-3 border border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent">
              <option>Tous les statuts</option>
              <option>En attente</option>
              <option>En cours</option>
              <option>Expédié</option>
              <option>Livré</option>
            </select>
          </div>
        </div>
      </motion.div>

      {/* Orders Table */}
      <motion.div
        className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {orders.length === 0 ? (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Inbox className="w-12 h-12 text-neutral-400" />
            </div>
            <h3 className="text-xl font-semibold text-neutral-900 mb-3">Aucune commande reçue</h3>
            <p className="text-neutral-500 mb-8 max-w-md mx-auto">
              Vos commandes apparaîtront ici dès que vos clients commenceront à acheter vos produits
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700">
                <Package className="w-4 h-4 mr-2" />
                Ajouter des produits
              </Button>
              <Button variant="outline" className="border-brand-200 text-brand-600 hover:bg-brand-50">
                Voir les statistiques
              </Button>
            </div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-neutral-50 border-b border-neutral-200">
                  <tr>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Commande</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Client</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Date</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Montant</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Statut</th>
                    <th className="text-left py-4 px-6 font-semibold text-neutral-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {orders.map((order, index) => (
                    <motion.tr
                      key={order.id}
                      className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-brand-100 rounded-lg flex items-center justify-center">
                            <ShoppingCart className="w-5 h-5 text-brand-600" />
                          </div>
                          <div>
                            <p className="font-medium text-neutral-900">{order.id}</p>
                            <p className="text-sm text-neutral-500">{order.items} articles</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div>
                          <p className="font-medium text-neutral-900">{order.customer}</p>
                          <p className="text-sm text-neutral-500">{order.email}</p>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-neutral-700">{order.date}</td>
                      <td className="py-4 px-6 font-semibold text-neutral-900">{order.amount}</td>
                      <td className="py-4 px-6">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          order.status === 'Livré' ? 'bg-emerald-100 text-emerald-700' :
                          order.status === 'En cours' ? 'bg-blue-100 text-blue-700' :
                          order.status === 'Expédié' ? 'bg-orange-100 text-orange-700' :
                          'bg-neutral-100 text-neutral-700'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm" className="hover:bg-blue-50 hover:text-blue-600">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="hover:bg-neutral-100">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* Pagination */}
            <div className="flex items-center justify-between p-6 border-t border-neutral-100 bg-neutral-50">
              <p className="text-sm text-neutral-600">
                Affichage de 0 à 0 sur 0 commandes
              </p>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>
                  Précédent
                </Button>
                <Button variant="outline" size="sm" disabled>
                  Suivant
                </Button>
              </div>
            </div>
          </>
        )}
      </motion.div>
    </div>
  );
}
