'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Mail, AlertCircle, CheckCircle } from 'lucide-react';
import { AuthLayout } from '@/components/auth/auth-layout';
import { FormField } from '@/components/auth/form-field';
import { PasswordField } from '@/components/auth/password-field';
import { LoadingButton } from '@/components/auth/loading-button';
import { useFormValidation } from '@/hooks/use-form-validation';
import { useAuth } from '@/hooks/use-auth';
import { clearAuthData } from '@/lib/auth-utils';

const validationRules = {
  email: [
    { required: true, message: 'L\'email est requis' },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Format d\'email invalide' }
  ],
  password: [
    { required: true, message: 'Le mot de passe est requis' },
    { minLength: 6, message: 'Le mot de passe doit contenir au moins 6 caractères' }
  ]
};

export default function LoginPage() {
  const router = useRouter();
  const { signIn, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email) {
      newErrors.email = 'L\'email est requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (!validateForm()) {
      return;
    }

    const { data, error } = await signIn(email, password);

    if (error) {
      setMessage({
        type: 'error',
        text: error.message === 'Invalid login credentials'
          ? 'Email ou mot de passe incorrect'
          : error.message
      });
    } else if (data.user) {
      setMessage({ type: 'success', text: 'Connexion réussie' });
      setTimeout(() => {
        router.push('/dashboard');
      }, 1500);
    }
  };

  return (
    <AuthLayout
      title="Connexion"
      subtitle="Connectez-vous à votre compte Sharyou"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Message */}
        {message && (
          <div className={`p-4 rounded-lg flex items-center gap-3 ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5 flex-shrink-0" />
            ) : (
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
            )}
            <span className="text-sm">{message.text}</span>
          </div>
        )}

        {/* Email Field */}
        <FormField
          id="email"
          name="email"
          type="email"
          label="Adresse email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          error={errors.email}
          icon={<Mail className="w-5 h-5" />}
          required
          autoComplete="email"
        />

        {/* Password Field */}
        <PasswordField
          id="password"
          name="password"
          label="Mot de passe"
          placeholder="••••••••"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          error={errors.password}
          required
          autoComplete="current-password"
        />

        {/* Remember Me & Forgot Password */}
        <div className="flex items-center justify-between">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
              className="w-4 h-4 text-brand-600 border-neutral-300 rounded focus:ring-brand-500 focus:ring-2"
            />
            <span className="text-sm text-neutral-700">
              Se souvenir de moi
            </span>
          </label>

          <Link
            href="/forgot-password"
            className="text-sm text-brand-600 hover:text-brand-700 font-medium transition-colors"
          >
            Mot de passe oublié ?
          </Link>
        </div>

        {/* Submit Button */}
        <LoadingButton
          type="submit"
          loading={loading}
          loadingText="Connexion..."
          className="w-full"
          size="lg"
        >
          Se connecter
        </LoadingButton>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-neutral-200" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-neutral-500">
              ou
            </span>
          </div>
        </div>

        {/* Sign Up Link */}
        <div className="text-center">
          <span className="text-sm text-neutral-600">
            Pas encore de compte ?{' '}
          </span>
          <Link
            href="/register"
            className="text-sm font-medium text-brand-600 hover:text-brand-700 transition-colors"
          >
            S'inscrire
          </Link>
        </div>
      </form>

      {/* Bouton de nettoyage d'urgence en mode développement */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800 mb-2">
            Mode développement : Nettoyer les données d'authentification corrompues
          </p>
          <button
            onClick={async () => {
              await clearAuthData();
              window.location.reload();
            }}
            className="text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700 transition-colors"
          >
            Nettoyer les données d'auth
          </button>
        </div>
      )}

    </AuthLayout>
  );
}
