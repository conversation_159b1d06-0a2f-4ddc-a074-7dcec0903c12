'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { MapPin, TrendingUp, Quote, Star, Clock, Package, Award } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';

export function TestimonialsSection() {
  const { t, isRTL } = useLanguage();
  const [activeTestimonial, setActiveTestimonial] = useState(0);
  
  const testimonials = t('testimonials.items');

  return (
    <section className="py-24 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('testimonials.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-4">
            {t('testimonials.subtitle')}
          </p>
          <p className="text-lg text-gray-500 max-w-4xl mx-auto">
            {t('testimonials.description')}
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial: any, index: number) => (
            <Card 
              key={index}
              className={`relative overflow-hidden transition-all duration-300 transform hover:scale-105 cursor-pointer ${
                activeTestimonial === index 
                  ? 'ring-2 ring-green-500 shadow-2xl' 
                  : 'hover:shadow-xl'
              }`}
              onClick={() => setActiveTestimonial(index)}
            >
              <CardContent className="p-8">
                {/* Quote Icon */}
                <div className="absolute top-4 right-4 w-12 h-12 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center">
                  <Quote className="h-6 w-6 text-green-600" />
                </div>

                {/* Rating */}
                <div className="flex gap-1 mb-6">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Quote */}
                <blockquote className="text-gray-700 mb-6 leading-relaxed text-lg italic">
                  "{testimonial.quote}"
                </blockquote>

                {/* Author Info */}
                <div className="flex items-center gap-4 mb-4">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback className="bg-gradient-to-br from-green-500 to-blue-500 text-white font-semibold">
                      {testimonial.name.split(' ').map((n: string) => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                    <p className="text-sm text-gray-600">{testimonial.business}</p>
                  </div>
                </div>

                {/* Location */}
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                  <MapPin className="h-4 w-4" />
                  {testimonial.location}
                </div>

                {/* Metrics */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>Temps de création:</span>
                    </div>
                    <span className="font-semibold text-green-600">{testimonial.timeToLaunch}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2 text-gray-600">
                      <TrendingUp className="h-4 w-4" />
                      <span>Croissance:</span>
                    </div>
                    <span className="font-semibold text-green-600">{testimonial.growth}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Package className="h-4 w-4" />
                      <span>Produits:</span>
                    </div>
                    <span className="font-medium text-gray-700 text-xs">{testimonial.products}</span>
                  </div>
                </div>

                {/* Revenue Badge */}
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-blue-100 text-green-700 px-4 py-2 rounded-full text-sm font-semibold mt-4">
                  <TrendingUp className="h-4 w-4" />
                  {testimonial.revenue}
                </div>

                {/* Achievement Badge */}
                {testimonial.achievement && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2 text-yellow-700">
                      <Award className="h-4 w-4" />
                      <span className="text-xs font-medium">{testimonial.achievement}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Featured Testimonial */}
        <div className="bg-white rounded-3xl p-12 shadow-2xl border border-gray-100">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-6 py-3 rounded-full mb-8">
              <TrendingUp className="h-5 w-5" />
              <span className="font-semibold">Success Story en vedette</span>
            </div>
            
            <div className="max-w-4xl mx-auto">
              <Quote className="h-16 w-16 text-green-500 mx-auto mb-6" />
              <blockquote className="text-2xl md:text-3xl text-gray-800 font-medium leading-relaxed mb-8 italic">
                "{testimonials[activeTestimonial].quote}"
              </blockquote>
              
              <div className="flex items-center justify-center gap-6">
                <Avatar className="h-16 w-16">
                  <AvatarFallback className="bg-gradient-to-br from-green-500 to-blue-500 text-white text-xl font-bold">
                    {testimonials[activeTestimonial].name.split(' ').map((n: string) => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <h4 className="text-xl font-bold text-gray-900">
                    {testimonials[activeTestimonial].name}
                  </h4>
                  <p className="text-gray-600">
                    {testimonials[activeTestimonial].business} • {testimonials[activeTestimonial].location}
                  </p>
                  <div className="flex items-center gap-2 mt-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span className="text-green-600 font-semibold">
                      {testimonials[activeTestimonial].revenue}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Success Statistics */}
        <div className="mt-20 grid md:grid-cols-4 gap-8 text-center">
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-green-600 mb-2">50M+</div>
            <p className="text-gray-600">{t('testimonials.stats.totalRevenue')}</p>
          </div>
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-blue-600 mb-2">350%</div>
            <p className="text-gray-600">{t('testimonials.stats.averageGrowth')}</p>
          </div>
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-purple-600 mb-2">96%</div>
            <p className="text-gray-600">{t('testimonials.stats.successRate')}</p>
          </div>
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="text-3xl font-bold text-orange-600 mb-2">30j</div>
            <p className="text-gray-600">{t('testimonials.stats.timeToProfit')}</p>
          </div>
        </div>
      </div>
    </section>
  );
}