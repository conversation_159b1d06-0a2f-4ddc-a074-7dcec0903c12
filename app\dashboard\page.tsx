'use client';

import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Users,
  Eye,
  DollarSign,
  Package,
  Clock,
  MoreHorizontal,
  Inbox,
  Activity
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

// Empty data for new store
const salesData = [
  { name: 'Lun', ventes: 0, commandes: 0 },
  { name: 'Mar', ventes: 0, commandes: 0 },
  { name: 'Mer', ventes: 0, commandes: 0 },
  { name: 'Jeu', ventes: 0, commandes: 0 },
  { name: 'Ven', ventes: 0, commandes: 0 },
  { name: 'Sam', ventes: 0, commandes: 0 },
  { name: 'Dim', ventes: 0, commandes: 0 },
];

const recentOrders: any[] = [];
const recentActivity: any[] = [];

const StatCard = ({ title, value, change, icon: Icon, trend, isZero }: any) => (
  <motion.div
    className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-all duration-200 group"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-10 h-10 bg-gradient-to-br from-brand-500 to-brand-600 rounded-lg flex items-center justify-center shadow-sm">
            <Icon className="w-5 h-5 text-white" />
          </div>
          <p className="text-sm font-semibold text-neutral-700 uppercase tracking-wide">{title}</p>
        </div>

        <div className="space-y-2">
          <p className="text-3xl font-bold text-neutral-900 tracking-tight">{value}</p>

          {!isZero && (
            <div className="flex items-center gap-1">
              {trend === 'up' ? (
                <TrendingUp className="w-4 h-4 text-emerald-500" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-500" />
              )}
              <span className={`text-sm font-medium ${trend === 'up' ? 'text-emerald-600' : 'text-red-600'}`}>
                {change}
              </span>
              <span className="text-sm text-neutral-500">vs hier</span>
            </div>
          )}

          {isZero && (
            <p className="text-sm text-neutral-500">Aucune donnée disponible</p>
          )}
        </div>
      </div>
    </div>
  </motion.div>
);

export default function DashboardPage() {
  return (
    <div className="space-y-6">


      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Ventes du jour"
          value="0 DA"
          change="0%"
          icon={DollarSign}
          trend="up"
          isZero={true}
        />
        <StatCard
          title="Commandes"
          value="0"
          change="0%"
          icon={ShoppingCart}
          trend="up"
          isZero={true}
        />
        <StatCard
          title="Visiteurs"
          value="0"
          change="0%"
          icon={Eye}
          trend="up"
          isZero={true}
        />
        <StatCard
          title="Taux de conversion"
          value="0%"
          change="0%"
          icon={TrendingUp}
          trend="up"
          isZero={true}
        />
      </div>

      {/* Charts Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-6">
          <div className="w-6 h-6 bg-gradient-to-br from-brand-500 to-brand-600 rounded flex items-center justify-center">
            <TrendingUp className="w-3 h-3 text-white" />
          </div>
          <h2 className="text-xl font-bold text-neutral-900">Analyse des performances</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sales Chart */}
          <motion.div
            className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-neutral-900">Ventes de la semaine</h3>
              </div>
              <button className="text-neutral-400 hover:text-neutral-600 transition-colors">
                <MoreHorizontal className="w-5 h-5" />
              </button>
            </div>

            <div className="h-80 flex items-center justify-center bg-neutral-50 rounded-lg border-2 border-dashed border-neutral-200">
              <div className="text-center">
                <DollarSign className="w-12 h-12 text-neutral-300 mx-auto mb-3" />
                <p className="text-neutral-500 font-medium">Aucune donnée de vente</p>
                <p className="text-sm text-neutral-400 mt-1">Les graphiques apparaîtront ici une fois que vous aurez des ventes</p>
              </div>
            </div>
          </motion.div>

          {/* Orders Chart */}
          <motion.div
            className="bg-white rounded-xl p-6 shadow-sm border border-neutral-200 hover:shadow-md transition-shadow"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <ShoppingCart className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-neutral-900">Commandes par jour</h3>
              </div>
              <button className="text-neutral-400 hover:text-neutral-600 transition-colors">
                <MoreHorizontal className="w-5 h-5" />
              </button>
            </div>

            <div className="h-80 flex items-center justify-center bg-neutral-50 rounded-lg border-2 border-dashed border-neutral-200">
              <div className="text-center">
                <ShoppingCart className="w-12 h-12 text-neutral-300 mx-auto mb-3" />
                <p className="text-neutral-500 font-medium">Aucune commande</p>
                <p className="text-sm text-neutral-400 mt-1">Les graphiques apparaîtront ici une fois que vous aurez des commandes</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Data Tables Section */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-6">
          <div className="w-6 h-6 bg-gradient-to-br from-brand-500 to-brand-600 rounded flex items-center justify-center">
            <Package className="w-3 h-3 text-white" />
          </div>
          <h2 className="text-xl font-bold text-neutral-900">Activité récente</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Orders */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-neutral-200 hover:shadow-md transition-shadow"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <div className="p-6 border-b border-neutral-100">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                  <ShoppingCart className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-neutral-900">Commandes récentes</h3>
              </div>
            </div>

            <div className="p-6">
              {recentOrders.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Inbox className="w-8 h-8 text-neutral-400" />
                  </div>
                  <p className="text-neutral-500 font-medium mb-2">Aucune commande récente</p>
                  <p className="text-sm text-neutral-400">Les commandes apparaîtront ici une fois que vous en recevrez</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentOrders.map((order, index) => (
                    <div key={order.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-neutral-50 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-brand-100 rounded-lg flex items-center justify-center">
                          <Package className="w-5 h-5 text-brand-600" />
                        </div>
                        <div>
                          <p className="font-medium text-neutral-900">{order.id}</p>
                          <p className="text-sm text-neutral-500">{order.customer}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-neutral-900">{order.amount}</p>
                        <div className="flex items-center gap-2">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            order.status === 'Livré' ? 'bg-emerald-100 text-emerald-700' :
                            order.status === 'En cours' ? 'bg-blue-100 text-blue-700' :
                            order.status === 'Expédié' ? 'bg-orange-100 text-orange-700' :
                            'bg-neutral-100 text-neutral-700'
                          }`}>
                            {order.status}
                          </span>
                          <span className="text-xs text-neutral-400">il y a {order.time}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            className="bg-white rounded-xl shadow-sm border border-neutral-200 hover:shadow-md transition-shadow"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <div className="p-6 border-b border-neutral-100">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Activity className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-neutral-900">Activité du compte</h3>
              </div>
            </div>

            <div className="p-6">
              {recentActivity.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-neutral-400" />
                  </div>
                  <p className="text-neutral-500 font-medium mb-2">Aucune activité récente</p>
                  <p className="text-sm text-neutral-400">L'historique de vos actions apparaîtra ici</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 rounded-lg hover:bg-neutral-50 transition-colors">
                      <div className="w-8 h-8 bg-brand-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                        <Clock className="w-4 h-4 text-brand-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-neutral-900">{activity.action}</p>
                        <p className="text-sm text-neutral-500">{activity.details}</p>
                        <p className="text-xs text-neutral-400 mt-1">il y a {activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
