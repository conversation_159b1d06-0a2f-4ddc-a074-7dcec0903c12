'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  ShoppingBag, 
  DollarSign,
  Star,
  Package,
  TrendingUp,
  Edit,
  MessageCircle,
  Gift
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  joinDate: string;
  totalOrders: number;
  totalSpent: number;
  lastOrder: string;
  status: string;
  segment: string;
  avatar?: string;
}

interface CustomerDetailModalProps {
  customer: Customer | null;
  isOpen: boolean;
  onClose: () => void;
}

export function CustomerDetailModal({ customer, isOpen, onClose }: CustomerDetailModalProps) {
  if (!customer) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) + ' DA';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  };

  const getSegmentInfo = (segment: string) => {
    switch (segment) {
      case 'vip':
        return { label: 'Client VIP', color: 'bg-yellow-100 text-yellow-800', icon: Star };
      case 'regular':
        return { label: 'Client Régulier', color: 'bg-blue-100 text-blue-800', icon: User };
      case 'new':
        return { label: 'Nouveau Client', color: 'bg-green-100 text-green-800', icon: TrendingUp };
      default:
        return { label: 'Client Standard', color: 'bg-gray-100 text-gray-800', icon: User };
    }
  };

  const segmentInfo = getSegmentInfo(customer.segment);
  const SegmentIcon = segmentInfo.icon;

  // Données simulées pour les commandes récentes
  const recentOrders = [
    {
      id: 'ORD-001',
      date: '2024-06-22',
      amount: 12500,
      status: 'delivered',
      items: 3
    },
    {
      id: 'ORD-002',
      date: '2024-06-15',
      amount: 8900,
      status: 'delivered',
      items: 2
    },
    {
      id: 'ORD-003',
      date: '2024-06-08',
      amount: 15600,
      status: 'delivered',
      items: 4
    }
  ];

  const customerMetrics = [
    {
      label: 'Commandes totales',
      value: customer.totalOrders.toString(),
      icon: ShoppingBag,
      color: 'blue'
    },
    {
      label: 'Total dépensé',
      value: formatCurrency(customer.totalSpent),
      icon: DollarSign,
      color: 'green'
    },
    {
      label: 'Panier moyen',
      value: formatCurrency(Math.round(customer.totalSpent / customer.totalOrders)),
      icon: Package,
      color: 'purple'
    },
    {
      label: 'Dernière commande',
      value: formatDate(customer.lastOrder),
      icon: Calendar,
      color: 'orange'
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Overlay */}
          <motion.div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl overflow-hidden max-h-[90vh] overflow-y-auto"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-brand-500 to-brand-700 px-8 py-6 text-white relative">
              <button
                onClick={onClose}
                className="absolute top-4 right-4 p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
              
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center text-2xl font-bold">
                  {customer.name.split(' ').map(n => n[0]).join('')}
                </div>
                
                <div className="flex-1">
                  <h2 className="text-2xl font-bold mb-1">{customer.name}</h2>
                  <div className="flex items-center gap-4 text-brand-100">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      {customer.email}
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      {customer.phone}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mt-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${segmentInfo.color} bg-white/20 text-white`}>
                      <SegmentIcon className="w-4 h-4 inline mr-1" />
                      {segmentInfo.label}
                    </span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      customer.status === 'active' ? 'bg-green-500/20 text-white' : 'bg-red-500/20 text-white'
                    }`}>
                      {customer.status === 'active' ? 'Actif' : 'Inactif'}
                    </span>
                  </div>
                </div>

                <div className="flex flex-col gap-2">
                  <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                    <Edit className="w-4 h-4 mr-2" />
                    Modifier
                  </Button>
                  <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Contacter
                  </Button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-8">
              {/* Customer Info */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                <div className="lg:col-span-1">
                  <h3 className="text-lg font-semibold text-neutral-900 mb-4">Informations personnelles</h3>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-neutral-400" />
                      <div>
                        <p className="text-sm text-neutral-600">Localisation</p>
                        <p className="font-medium text-neutral-900">{customer.location}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="w-5 h-5 text-neutral-400" />
                      <div>
                        <p className="text-sm text-neutral-600">Membre depuis</p>
                        <p className="font-medium text-neutral-900">{formatDate(customer.joinDate)}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="lg:col-span-2">
                  <h3 className="text-lg font-semibold text-neutral-900 mb-4">Métriques client</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {customerMetrics.map((metric, index) => (
                      <div key={index} className="bg-neutral-50 rounded-lg p-4">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 bg-${metric.color}-100 rounded-lg flex items-center justify-center`}>
                            <metric.icon className={`w-5 h-5 text-${metric.color}-600`} />
                          </div>
                          <div>
                            <p className="text-sm text-neutral-600">{metric.label}</p>
                            <p className="font-semibold text-neutral-900">{metric.value}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Recent Orders */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-neutral-900">Commandes récentes</h3>
                  <Button variant="outline" size="sm" className="border-brand-200 text-brand-600 hover:bg-brand-50">
                    Voir toutes les commandes
                  </Button>
                </div>
                
                <div className="bg-neutral-50 rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-neutral-100">
                      <tr>
                        <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600">Commande</th>
                        <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600">Date</th>
                        <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600">Articles</th>
                        <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600">Montant</th>
                        <th className="text-left py-3 px-4 text-sm font-medium text-neutral-600">Statut</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-neutral-200">
                      {recentOrders.map((order) => (
                        <tr key={order.id} className="hover:bg-white transition-colors">
                          <td className="py-3 px-4">
                            <p className="font-medium text-neutral-900">{order.id}</p>
                          </td>
                          <td className="py-3 px-4">
                            <p className="text-neutral-600">{formatDate(order.date)}</p>
                          </td>
                          <td className="py-3 px-4">
                            <p className="text-neutral-600">{order.items} articles</p>
                          </td>
                          <td className="py-3 px-4">
                            <p className="font-semibold text-neutral-900">{formatCurrency(order.amount)}</p>
                          </td>
                          <td className="py-3 px-4">
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Livrée
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3 mt-8 pt-6 border-t border-neutral-200">
                <Button variant="outline" onClick={onClose}>
                  Fermer
                </Button>
                <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700">
                  <Gift className="w-4 h-4 mr-2" />
                  Offrir une réduction
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
