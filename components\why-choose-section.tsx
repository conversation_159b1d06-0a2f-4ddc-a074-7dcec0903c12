'use client';

import { Shield, Zap, Globe, Users } from 'lucide-react';
import { useLanguage } from '@/hooks/use-language';

export function WhyChooseSection() {
  const { t, isRTL } = useLanguage();
  const reasons = t('whyChoose.reasons');

  const icons = [Zap, Shield, Globe, Users];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('whyChoose.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('whyChoose.subtitle')}
          </p>
        </div>

        {/* Reasons Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {reasons.map((reason: any, index: number) => {
            const Icon = icons[index];
            
            return (
              <div 
                key={index}
                className="text-center group hover:transform hover:scale-105 transition-all duration-300"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-xl transition-shadow duration-300">
                  <Icon className="h-10 w-10 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {reason.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {reason.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Trust Indicators */}
        <div className="mt-20 bg-gradient-to-r from-green-50 to-blue-50 rounded-3xl p-12">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              Certifications et Conformité
            </h3>
            <div className="flex flex-wrap justify-center items-center gap-8 text-gray-600">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                <span className="font-medium">ISO 27001 Certifié</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600" />
                <span className="font-medium">Conforme RGPD</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-purple-600" />
                <span className="font-medium">Agréé Banque d'Algérie</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
