import { supabase } from './supabase';

/**
 * Nettoie les données d'authentification corrompues
 * Utile quand il y a des erreurs de refresh token
 */
export const clearAuthData = async () => {
  try {
    // Supprimer la session Supabase
    await supabase.auth.signOut();
    
    // Nettoyer le localStorage manuellement si nécessaire
    if (typeof window !== 'undefined') {
      // Supprimer toutes les clés liées à Supabase
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('sb-')) {
          keysToRemove.push(key);
        }
      }
      
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      
      // Nettoyer aussi le sessionStorage
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith('sb-')) {
          sessionStorage.removeItem(key);
        }
      }
    }
    
    console.log('Auth data cleared successfully');
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};

/**
 * Vérifie si la session actuelle est valide
 */
export const validateSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Session validation error:', error);
      await clearAuthData();
      return false;
    }
    
    if (!session) {
      return false;
    }
    
    // Vérifier si le token n'est pas expiré
    const now = Math.floor(Date.now() / 1000);
    if (session.expires_at && session.expires_at < now) {
      console.log('Session expired, clearing auth data');
      await clearAuthData();
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error validating session:', error);
    await clearAuthData();
    return false;
  }
};

/**
 * Rafraîchit la session de manière sécurisée
 */
export const refreshSession = async () => {
  try {
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('Error refreshing session:', error);
      await clearAuthData();
      return null;
    }
    
    return data.session;
  } catch (error) {
    console.error('Failed to refresh session:', error);
    await clearAuthData();
    return null;
  }
};
