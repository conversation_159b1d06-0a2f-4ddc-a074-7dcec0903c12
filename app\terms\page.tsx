'use client';

import { motion } from 'framer-motion';
import { ArrowLeft, FileText, Scale, Shield, Bot, Users, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

export default function TermsPage() {
  const router = useRouter();

  const sections = [
    {
      id: 'general',
      title: '1. DISPOSITIONS GÉNÉRALES',
      icon: FileText,
      content: [
        {
          subtitle: '1.1 Présentation de la Plateforme',
          text: 'Sharyou est une plateforme de commerce électronique constituée sous forme de société de droit algérien, immatriculée au Registre du Commerce Algérien sous le numéro [À compléter], ayant son siège social à [Adresse à compléter], Algérie.'
        },
        {
          subtitle: '1.2 Objet des Conditions Générales',
          text: 'Les présentes Conditions Générales d\'Utilisation (CGU) régissent l\'utilisation de la plateforme Sharyou, conformément à la Loi n° 18-05 du 10 mai 2018 relative au commerce électronique et aux dispositions du Code Civil Algérien.'
        },
        {
          subtitle: '1.3 Acceptation des Conditions',
          text: 'L\'utilisation de la plateforme Sharyou implique l\'acceptation pleine et entière des présentes CGU. Toute utilisation de nos services constitue une acceptation tacite de ces conditions.'
        }
      ]
    },
    {
      id: 'definitions',
      title: '2. DÉFINITIONS',
      icon: Globe,
      content: [
        {
          text: '• Plateforme : Le site web et l\'application mobile Sharyou\n• Utilisateur : Toute personne utilisant la plateforme Sharyou\n• Vendeur : Commerçant proposant ses produits sur la plateforme\n• Acheteur : Personne effectuant un achat sur la plateforme\n• e-Fournisseur : Au sens de la Loi 18-05, le vendeur proposant des biens ou services par voie électronique\n• e-Consommateur : Au sens de la Loi 18-05, la personne physique qui acquiert des biens ou services pour ses besoins personnels'
        }
      ]
    },
    {
      id: 'account',
      title: '3. INSCRIPTION ET COMPTE UTILISATEUR',
      icon: Users,
      content: [
        {
          subtitle: '3.1 Conditions d\'Inscription',
          text: 'Pour utiliser la plateforme Sharyou, l\'utilisateur doit :\n• Être âgé de 18 ans révolus ou être représenté par son tuteur légal\n• Fournir des informations exactes et complètes\n• Accepter les présentes CGU et la Politique de Confidentialité'
        },
        {
          subtitle: '3.2 Vérification d\'Identité',
          text: 'Conformément à la réglementation algérienne, Sharyou peut demander la vérification de l\'identité des utilisateurs par la fourniture de documents officiels.'
        },
        {
          subtitle: '3.3 Responsabilité du Compte',
          text: 'L\'utilisateur est responsable de :\n• La confidentialité de ses identifiants de connexion\n• Toutes les activités effectuées sous son compte\n• La mise à jour de ses informations personnelles'
        }
      ]
    },
    {
      id: 'ai-tools',
      title: '8. UTILISATION DES OUTILS D\'INTELLIGENCE ARTIFICIELLE',
      icon: Bot,
      content: [
        {
          subtitle: '8.1 Services IA Proposés',
          text: 'Sharyou met à disposition de ses utilisateurs des outils d\'intelligence artificielle incluant :\n• Génération automatique de descriptions de produits\n• Optimisation des images et redimensionnement automatique\n• Recommandations personnalisées pour les acheteurs\n• Assistance chatbot pour le service client\n• Analyse de sentiments sur les avis clients\n• Traduction automatique des contenus\n• Optimisation SEO automatique des fiches produits\n• Détection automatique de fraude et contenus inappropriés'
        },
        {
          subtitle: '8.2 Conditions d\'Utilisation des Outils IA',
          text: 'L\'accès aux outils IA peut être inclus dans l\'offre de base ou faire l\'objet d\'un abonnement supplémentaire. L\'utilisateur demeure entièrement responsable du contenu généré par les outils IA et doit vérifier tout contenu avant publication.'
        },
        {
          subtitle: '8.3 Limitations et Exclusions de Responsabilité IA',
          text: 'Sharyou ne garantit pas la disponibilité continue des outils IA. Les services IA peuvent présenter des erreurs, imprécisions ou biais. L\'utilisateur s\'engage à utiliser les outils IA en conformité avec la réglementation algérienne.'
        },
        {
          subtitle: '8.5 Interdictions Spécifiques aux Outils IA',
          text: 'Il est strictement interdit d\'utiliser les outils IA pour :\n• Générer des contenus illégaux, diffamatoires ou contraires à l\'ordre public\n• Créer des descriptions de produits fausses ou trompeuses\n• Générer des avis clients fictifs ou manipuler les évaluations\n• Violer les droits de propriété intellectuelle d\'autrui\n• Créer des contenus discriminatoires ou offensants'
        }
      ]
    },
    {
      id: 'consumer-protection',
      title: '5. PROTECTION DES CONSOMMATEURS',
      icon: Shield,
      content: [
        {
          subtitle: '5.1 Droit d\'Information',
          text: 'Conformément à la Loi 18-05, avant toute commande, le consommateur doit être informé de :\n• L\'identité complète du vendeur\n• Les caractéristiques essentielles du produit\n• Le prix total incluant tous les frais\n• Les modalités de paiement et de livraison\n• Les conditions d\'exercice du droit de rétractation'
        },
        {
          subtitle: '5.2 Droit de Rétractation',
          text: 'Le consommateur dispose d\'un délai de 7 jours ouvrables pour exercer son droit de rétractation à compter de la réception du produit. Le produit doit être retourné dans son état d\'origine.'
        },
        {
          subtitle: '5.3 Garantie Légale',
          text: 'Tous les produits vendus bénéficient de la garantie légale de conformité prévue par le droit algérien pour une durée minimale de 6 mois.'
        }
      ]
    },
    {
      id: 'sellers',
      title: '4. OBLIGATIONS DES VENDEURS',
      icon: Users,
      content: [
        {
          subtitle: '4.1 Obligations Légales',
          text: 'Les vendeurs s\'engagent à :\n• Respecter la législation algérienne en vigueur\n• Fournir des informations exactes sur leurs produits\n• Respecter les prix affichés et les conditions de vente\n• Assurer la disponibilité des produits proposés\n• Respecter les délais de livraison annoncés'
        },
        {
          subtitle: '4.2 Étiquetage et Information Produits',
          text: 'Conformément à la réglementation algérienne, les produits doivent être étiquetés en langue arabe et inclure :\n• Dénomination du produit\n• Prix en Dinars Algériens (DZD)\n• Origine du produit\n• Date de péremption le cas échéant\n• Conditions de conservation'
        }
      ]
    },
    {
      id: 'pricing',
      title: '6. PRIX ET MODALITÉS DE PAIEMENT',
      icon: Scale,
      content: [
        {
          subtitle: '6.1 Prix',
          text: '• Tous les prix sont exprimés en Dinars Algériens (DZD) toutes taxes comprises\n• Les prix incluent la TVA au taux en vigueur\n• Sharyou se réserve le droit de modifier ses tarifs à tout moment'
        },
        {
          subtitle: '6.2 Moyens de Paiement',
          text: 'Les moyens de paiement acceptés sont :\n• Paiement à la livraison (cash on delivery)\n• Cartes bancaires émises par les banques algériennes\n• Virements bancaires\n• Tout autre moyen de paiement agréé par la Banque d\'Algérie'
        },
        {
          subtitle: '6.3 Facturation',
          text: 'Conformément à la législation fiscale algérienne, une facture en langue arabe sera émise pour chaque achat effectué sur la plateforme.'
        }
      ]
    },
    {
      id: 'delivery',
      title: '7. LIVRAISON',
      icon: Globe,
      content: [
        {
          subtitle: '7.1 Zones de Livraison',
          text: 'La livraison s\'effectue uniquement sur le territoire algérien dans les zones desservies par nos partenaires logistiques.'
        },
        {
          subtitle: '7.2 Délais de Livraison',
          text: 'Les délais de livraison sont communiqués à titre indicatif et peuvent varier selon :\n• La localisation géographique\n• La disponibilité du produit\n• Les conditions climatiques ou autres circonstances exceptionnelles'
        },
        {
          subtitle: '7.3 Modalités de Livraison',
          text: '• La livraison s\'effectue à l\'adresse indiquée par l\'acheteur\n• Une pièce d\'identité peut être demandée lors de la livraison\n• En cas d\'absence, le transporteur peut procéder à une seconde tentative'
        }
      ]
    },
    {
      id: 'intellectual-property',
      title: '9. PROPRIÉTÉ INTELLECTUELLE',
      icon: Shield,
      content: [
        {
          subtitle: '9.1 Propriété de Sharyou',
          text: 'Sharyou est propriétaire de tous les droits de propriété intellectuelle relatifs à :\n• La marque Sharyou\n• Le design et l\'interface de la plateforme\n• Les contenus éditoriaux\n• Les bases de données\n• Les algorithmes et modèles d\'intelligence artificielle\n• Les outils IA et leurs fonctionnalités'
        },
        {
          subtitle: '9.2 Contenu des Utilisateurs',
          text: 'Les utilisateurs conservent leurs droits sur les contenus qu\'ils publient mais accordent à Sharyou une licence d\'utilisation pour les besoins de la plateforme, y compris pour l\'entraînement et l\'amélioration des outils IA.'
        },
        {
          subtitle: '9.3 Contenu Généré par IA',
          text: 'Le contenu créé à l\'aide des outils IA de Sharyou appartient à l\'utilisateur qui l\'a généré, sous réserve du respect des présentes CGU et de la réglementation en vigueur.'
        }
      ]
    },
    {
      id: 'data-protection',
      title: '10. DONNÉES PERSONNELLES',
      icon: Shield,
      content: [
        {
          subtitle: '10.1 Collecte et Traitement',
          text: 'Sharyou collecte et traite les données personnelles conformément à :\n• La Loi n° 18-07 du 10 juin 2018 relative à la protection des personnes physiques dans le traitement des données à caractère personnel\n• Notre Politique de Confidentialité détaillée\n• Les exigences spécifiques liées à l\'utilisation des outils d\'intelligence artificielle'
        },
        {
          subtitle: '10.2 Hébergement des Données',
          text: 'Conformément à la réglementation algérienne, toutes les données sont hébergées sur le territoire algérien, y compris les données utilisées par les services IA.'
        },
        {
          subtitle: '10.3 Données et Intelligence Artificielle',
          text: '• Les données utilisées par les outils IA sont anonymisées et agrégées\n• Les données personnelles ne sont pas utilisées pour entraîner les modèles IA sans consentement explicite\n• Les utilisateurs peuvent s\'opposer à l\'utilisation de leurs données pour l\'amélioration des services IA'
        }
      ]
    },
    {
      id: 'liability',
      title: '11. RESPONSABILITÉ ET GARANTIES',
      icon: Scale,
      content: [
        {
          subtitle: '11.1 Responsabilité de Sharyou',
          text: 'Sharyou agit en tant qu\'intermédiaire technique et ne peut être tenu responsable :\n• De la qualité des produits vendus par les vendeurs\n• Du contenu des annonces publiées par les vendeurs\n• Des dysfonctionnements dus à des causes externes\n• De l\'exactitude du contenu généré par les outils d\'intelligence artificielle'
        },
        {
          subtitle: '11.2 Limitation de Responsabilité',
          text: 'La responsabilité de Sharyou est limitée au montant de la transaction concernée, sauf en cas de faute lourde ou intentionnelle. Cette limitation s\'applique également aux services d\'intelligence artificielle.'
        },
        {
          subtitle: '11.3 Responsabilité des Utilisateurs des Outils IA',
          text: 'Les utilisateurs sont entièrement responsables :\n• De la vérification du contenu généré par IA avant publication\n• Du respect de la réglementation lors de l\'utilisation des outils IA\n• Des conséquences de l\'utilisation du contenu généré par IA'
        }
      ]
    },
    {
      id: 'disputes',
      title: '12. RÉSOLUTION DES LITIGES',
      icon: Scale,
      content: [
        {
          subtitle: '12.1 Médiation',
          text: 'En cas de litige, y compris ceux relatifs à l\'utilisation des outils d\'intelligence artificielle, les parties s\'engagent à rechercher une solution amiable avant tout recours judiciaire.'
        },
        {
          subtitle: '12.2 Compétence Juridictionnelle',
          text: 'En cas d\'échec de la médiation, les tribunaux algériens sont seuls compétents pour connaître des litiges relatifs à l\'utilisation de la plateforme Sharyou et de ses services IA.'
        },
        {
          subtitle: '12.3 Droit Applicable',
          text: 'Les présentes CGU sont régies par le droit algérien, y compris pour les aspects relatifs à l\'intelligence artificielle.'
        }
      ]
    },
    {
      id: 'acceptable-use',
      title: '13. UTILISATION ACCEPTABLE',
      icon: Shield,
      content: [
        {
          subtitle: '13.1 Interdictions',
          text: 'Il est strictement interdit d\'utiliser la plateforme pour :\n• Vendre des produits illégaux ou non conformes à la réglementation algérienne\n• Publier des contenus contraires à l\'ordre public et aux bonnes mœurs\n• Porter atteinte aux droits d\'autrui\n• Diffuser des informations fausses ou trompeuses'
        },
        {
          subtitle: '13.2 Sanctions',
          text: 'En cas de violation des présentes CGU, Sharyou peut :\n• Suspendre ou supprimer le compte utilisateur\n• Supprimer les contenus litigieux\n• Engager des poursuites judiciaires'
        }
      ]
    },
    {
      id: 'modifications',
      title: '14. MODIFICATION DES CONDITIONS',
      icon: FileText,
      content: [
        {
          subtitle: '14.1 Droit de Modification',
          text: 'Sharyou se réserve le droit de modifier les présentes CGU à tout moment, y compris les conditions d\'utilisation des outils d\'intelligence artificielle.'
        },
        {
          subtitle: '14.2 Information des Utilisateurs',
          text: 'Les utilisateurs seront informés de toute modification, particulièrement celles affectant les services IA, par :\n• Notification sur la plateforme\n• Envoi d\'un email à l\'adresse renseignée\n• Publication sur le site web'
        },
        {
          subtitle: '14.3 Acceptation des Modifications',
          text: 'La poursuite de l\'utilisation de la plateforme et de ses outils IA après notification des modifications vaut acceptation des nouvelles conditions.'
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Header */}
      <div className="bg-white border-b border-neutral-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="flex items-center gap-2 text-neutral-600 hover:text-neutral-900"
            >
              <ArrowLeft className="w-4 h-4" />
              Retour
            </Button>
            <div className="h-6 w-px bg-neutral-300" />
            <div>
              <h1 className="text-2xl font-bold text-neutral-900">Conditions Générales d'Utilisation</h1>
              <p className="text-sm text-neutral-600 mt-1">Dernière mise à jour : 26 juin 2025</p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Introduction */}
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-brand-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <FileText className="w-6 h-6 text-brand-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-neutral-900 mb-2">À propos de ce document</h2>
              <p className="text-neutral-600 leading-relaxed">
                Ces Conditions Générales d'Utilisation régissent votre utilisation de la plateforme Sharyou, 
                incluant nos services d'intelligence artificielle innovants. Elles sont conformes à la 
                législation algérienne en vigueur et protègent vos droits en tant qu'utilisateur.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Sections */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={section.id}
              className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="bg-gradient-to-r from-brand-50 to-accent-50 px-6 py-4 border-b border-neutral-100">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-brand-500 rounded-lg flex items-center justify-center">
                    <section.icon className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-900">{section.title}</h3>
                </div>
              </div>
              
              <div className="p-6 space-y-6">
                {section.content.map((item, itemIndex) => (
                  <div key={itemIndex}>
                    {'subtitle' in item && item.subtitle && (
                      <h4 className="font-semibold text-neutral-900 mb-2">{item.subtitle}</h4>
                    )}
                    <div className="text-neutral-700 leading-relaxed whitespace-pre-line">
                      {item.text}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Legal Compliance */}
        <motion.div
          className="bg-gradient-to-r from-brand-500 to-brand-700 rounded-xl text-white p-6 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.8 }}
        >
          <h3 className="text-lg font-semibold mb-4">Déclaration de Conformité</h3>
          <p className="text-brand-100 leading-relaxed mb-4">
            Les présentes Conditions Générales d'Utilisation ont été établies en conformité avec :
          </p>
          <ul className="text-brand-100 space-y-1 text-sm">
            <li>• La Loi n° 18-05 du 10 mai 2018 relative au commerce électronique</li>
            <li>• La Loi n° 18-07 du 10 juin 2018 relative à la protection des données personnelles</li>
            <li>• Le Code Civil Algérien</li>
            <li>• Le Code de Commerce Algérien</li>
            <li>• La Loi n° 09-03 du 25 février 2009 relative à la protection du consommateur</li>
          </ul>
          <div className="mt-4 pt-4 border-t border-brand-400">
            <p className="text-sm text-brand-100">
              <strong>Date d'entrée en vigueur :</strong> 26 juin 2025
            </p>
          </div>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          className="bg-white rounded-xl shadow-sm border border-neutral-200 p-6 mt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.9 }}
        >
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">Contact</h3>
          <p className="text-neutral-600 mb-4">
            Pour toute question relative aux présentes CGU ou à l'utilisation des outils d'intelligence artificielle :
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-neutral-900">Sharyou</p>
              <p className="text-neutral-600">Email : <EMAIL></p>
              <p className="text-neutral-600">Support IA : <EMAIL></p>
            </div>
            <div>
              <p className="text-neutral-600">Adresse : [Adresse complète]</p>
              <p className="text-neutral-600">Téléphone : [Numéro de téléphone]</p>
              <p className="text-neutral-600">RC : [Numéro RC]</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
